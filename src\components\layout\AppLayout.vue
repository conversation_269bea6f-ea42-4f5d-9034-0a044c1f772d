<template>
  <div class="app-layout">
    <el-container class="layout-container" direction="vertical">
      <!-- 顶部导航栏 -->
      <AppHeader
        :user-info="userInfo"
        :collapsed="sidebarCollapsed"
        @toggle-sidebar="toggleSidebar"
        @user-command="$emit('user-command', $event)"
      />

      <el-container class="main-container" direction="horizontal">
        <!-- 侧边栏 -->
        <AppSidebar
          :menu-list="menuList"
          :active-menu="activeMenu"
          :collapsed="sidebarCollapsed"
          @menu-select="$emit('menu-select', $event)"
        />

        <!-- 主内容区 -->
        <AppMain>
          <slot />
        </AppMain>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import { ref } from 'vue'
import AppHeader from './AppHeader.vue'
import AppSidebar from './AppSidebar.vue'
import AppMain from './AppMain.vue'

export default {
  name: 'AppLayout',
  components: {
    AppHeader,
    AppSidebar,
    AppMain
  },
  props: {
    menuList: {
      type: Array,
      default: () => []
    },
    userInfo: {
      type: Object,
      default: () => ({})
    },
    activeMenu: {
      type: String,
      default: ''
    }
  },
  emits: ['menu-select', 'user-command'],
  setup() {
    const sidebarCollapsed = ref(false)
    
    const toggleSidebar = () => {
      sidebarCollapsed.value = !sidebarCollapsed.value
    }
    
    return {
      sidebarCollapsed,
      toggleSidebar
    }
  }
}
</script>

<style scoped>
.app-layout {
  height: 100vh;
  overflow: hidden;
}

.layout-container {
  height: 100%;
  width: 100%;
}

.main-container {
  height: calc(100vh - 60px);
  flex: 1;
}
</style>
