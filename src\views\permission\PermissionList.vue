<template>
  <div class="permission-list">
    <!-- 搜索表单 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item label="模块">
          <el-input v-model="searchForm.module" placeholder="请输入模块名称" clearable />
        </el-form-item>
        <el-form-item label="功能名称">
          <el-input v-model="searchForm.function_name" placeholder="请输入功能名称" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="handleAdd">新增权限</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card" shadow="never">
      <el-table :data="tableData" border stripe>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="权限名称" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="module" label="模块" />
        <el-table-column prop="function_name" label="功能名称" />
        <el-table-column prop="created_at" label="创建时间" width="180" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增权限' : '编辑权限'"
      width="500px"
    >
      <el-form
        ref="permissionFormRef"
        :model="permissionForm"
        :rules="permissionFormRules"
        label-width="100px"
      >
        <el-form-item label="权限名称" prop="name">
          <el-input v-model="permissionForm.name" placeholder="请输入权限名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="permissionForm.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="模块" prop="module">
          <el-input v-model="permissionForm.module" placeholder="请输入模块名称" />
        </el-form-item>
        <el-form-item label="功能名称" prop="function_name">
          <el-input v-model="permissionForm.function_name" placeholder="请输入功能名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getPermissions, createPermission, updatePermission, deletePermission } from '../../api/permission'

// 搜索表单数据
const searchForm = ref({
  module: '',
  function_name: ''
})

// 表格数据
const tableData = ref([])
const total = ref(0)

// 分页参数
const pagination = ref({
  pageNum: 1,
  pageSize: 10
})

// 对话框相关数据
const dialogVisible = ref(false)
const dialogType = ref('add')
const permissionFormRef = ref(null)
const permissionForm = ref({
  name: '',
  description: '',
  module: '',
  function_name: ''
})

// 表单验证规则
const permissionFormRules = {
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入描述', trigger: 'blur' }
  ],
  module: [
    { required: true, message: '请输入模块名称', trigger: 'blur' }
  ],
  function_name: [
    { required: true, message: '请输入功能名称', trigger: 'blur' }
  ]
}

// 获取权限列表数据
const getPermissionList = async () => {
  try {
    const params = {
      ...searchForm.value,
      ...pagination.value
    }
    const response = await getPermissions(params)
    if (response.data.code === 200) {
      tableData.value = response.data.data.list
      total.value = response.data.data.total
    } else {
      ElMessage.error(response.data.message || '获取权限列表失败')
    }
  } catch (error) {
    console.error('获取权限列表失败:', error)
    ElMessage.error('获取权限列表失败')
  }
}

// 搜索
const handleSearch = () => {
  pagination.value.pageNum = 1
  getPermissionList()
}

// 重置
const handleReset = () => {
  searchForm.value = {
    module: '',
    function_name: ''
  }
  pagination.value = {
    pageNum: 1,
    pageSize: 10
  }
  getPermissionList()
}

// 分页大小改变
const handleSizeChange = (val) => {
  pagination.value.pageSize = val
  getPermissionList()
}

// 页码改变
const handleCurrentChange = (val) => {
  pagination.value.pageNum = val
  getPermissionList()
}

// 打开新增权限对话框
const handleAdd = () => {
  dialogType.value = 'add'
  permissionForm.value = {
    name: '',
    description: '',
    module: '',
    function_name: ''
  }
  dialogVisible.value = true
}

// 编辑权限
const handleEdit = (row) => {
  dialogType.value = 'edit'
  permissionForm.value = {
    id: row.id,
    name: row.name,
    description: row.description,
    module: row.module,
    function_name: row.function_name
  }
  dialogVisible.value = true
}

// 删除权限
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除权限 "${row.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await deletePermission(row.id)
    if (response.data.code === 200) {
      ElMessage.success('删除成功')
      getPermissionList()
    } else {
      ElMessage.error(response.data.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除权限失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!permissionFormRef.value) return
  
  await permissionFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        let response
        if (dialogType.value === 'add') {
          response = await createPermission(permissionForm.value)
        } else {
          response = await updatePermission(permissionForm.value.id, permissionForm.value)
        }
        
        if (response.data.code === 200) {
          ElMessage.success(dialogType.value === 'add' ? '新增成功' : '更新成功')
          dialogVisible.value = false
          getPermissionList()
        } else {
          ElMessage.error(response.data.message || '操作失败')
        }
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error('操作失败')
      }
    }
  })
}

// 页面加载时获取数据
onMounted(() => {
  getPermissionList()
})
</script>

<style scoped>
.permission-list {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}
</style>
