// 全局样式变量
:root {
  // 主色调
  --primary-color: #409eff;
  --primary-light: #79bbff;
  --primary-dark: #337ecc;
  
  // 辅助色
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  
  // 中性色
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #c0c4cc;
  
  // 边框色
  --border-base: #dcdfe6;
  --border-light: #e4e7ed;
  --border-lighter: #ebeef5;
  --border-extra-light: #f2f6fc;
  
  // 背景色
  --bg-color: #ffffff;
  --bg-page: #f5f7fa;
  --bg-overlay: rgba(0, 0, 0, 0.8);
  
  // 阴影
  --shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --shadow-dark: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  // 圆角
  --border-radius-base: 4px;
  --border-radius-small: 2px;
  --border-radius-large: 8px;
  --border-radius-round: 20px;
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  // 字体大小
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;
  
  // 动画时间
  --transition-base: 0.3s;
  --transition-fast: 0.2s;
  --transition-slow: 0.5s;
}

// 全局重置样式
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  background-color: var(--bg-page);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
  width: 100%;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-page);
  border-radius: var(--border-radius-base);
}

::-webkit-scrollbar-thumb {
  background: var(--border-base);
  border-radius: var(--border-radius-base);
  transition: background var(--transition-base);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-placeholder);
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-center { 
  display: flex; 
  align-items: center; 
  justify-content: center; 
}
.flex-between { 
  display: flex; 
  align-items: center; 
  justify-content: space-between; 
}
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-1 { flex: 1; }

.hidden { display: none; }
.visible { visibility: visible; }
.invisible { visibility: hidden; }

// 间距工具类
@for $i from 0 through 10 {
  .m-#{$i} { margin: #{$i * 4}px; }
  .mt-#{$i} { margin-top: #{$i * 4}px; }
  .mr-#{$i} { margin-right: #{$i * 4}px; }
  .mb-#{$i} { margin-bottom: #{$i * 4}px; }
  .ml-#{$i} { margin-left: #{$i * 4}px; }
  .mx-#{$i} { 
    margin-left: #{$i * 4}px; 
    margin-right: #{$i * 4}px; 
  }
  .my-#{$i} { 
    margin-top: #{$i * 4}px; 
    margin-bottom: #{$i * 4}px; 
  }
  
  .p-#{$i} { padding: #{$i * 4}px; }
  .pt-#{$i} { padding-top: #{$i * 4}px; }
  .pr-#{$i} { padding-right: #{$i * 4}px; }
  .pb-#{$i} { padding-bottom: #{$i * 4}px; }
  .pl-#{$i} { padding-left: #{$i * 4}px; }
  .px-#{$i} { 
    padding-left: #{$i * 4}px; 
    padding-right: #{$i * 4}px; 
  }
  .py-#{$i} { 
    padding-top: #{$i * 4}px; 
    padding-bottom: #{$i * 4}px; 
  }
}

// 响应式断点
$breakpoints: (
  xs: 480px,
  sm: 768px,
  md: 992px,
  lg: 1200px,
  xl: 1920px
);

// 响应式工具类
@each $name, $size in $breakpoints {
  @media (max-width: $size) {
    .hidden-#{$name}-down { display: none !important; }
  }
  
  @media (min-width: $size + 1) {
    .hidden-#{$name}-up { display: none !important; }
  }
}

// 卡片样式
.card {
  background: var(--bg-color);
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-light);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  transition: box-shadow var(--transition-base);

  &:hover {
    box-shadow: var(--shadow-dark);
  }
}

// 统一卡片标题样式
.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

// 统一刷新按钮样式
.refresh-btn {
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
  }

  .el-icon {
    margin-right: 8px;
  }
}

// 统一卡片头部样式
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

// 按钮增强样式
.btn-gradient {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border: none;
  color: white;
  transition: all var(--transition-base);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  }
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-base);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform var(--transition-base);
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

// Element Plus 组件样式覆盖
.el-button {
  border-radius: var(--border-radius-base);
  transition: all var(--transition-base);
}

.el-card {
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-light);
}

.el-table {
  border-radius: var(--border-radius-base);
  overflow: hidden;
}

.el-dialog {
  border-radius: var(--border-radius-large);
}

.el-form-item__label {
  font-weight: 500;
  color: var(--text-primary);
}

// 加载状态
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--text-secondary);
}

// 空状态
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: var(--text-secondary);
  
  .empty-icon {
    font-size: 64px;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
  }
  
  .empty-text {
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-sm);
  }
  
  .empty-description {
    font-size: var(--font-size-sm);
    color: var(--text-placeholder);
  }
}
