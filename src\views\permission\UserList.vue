<template>
  <div class="user-list-container">
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="姓名">
          <el-input v-model="searchForm.username" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="searchForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="operation-area">
      <el-button type="primary" @click="handleAdd">新增</el-button>
    </div>

    <!-- 表格区域 -->
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="username" label="姓名" />
      <el-table-column prop="email" label="邮箱" width="250" />
      <el-table-column prop="phone" label="手机号"/>
      <el-table-column prop="role" label="角色"/>
      <el-table-column prop="createTime" label="创建时间"/>
      <el-table-column prop="lastLoginTime" label="最后一次登录时间"/>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 在表格区域后添加对话框组件 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增用户' : '编辑用户'"
      width="500px"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userFormRules"
        label-width="80px"
      >
        <el-form-item label="姓名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="userForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="userForm.password" type="password" placeholder="请输入密码" />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-checkbox v-model="isAdmin" @change="handleRoleChange">管理员</el-checkbox>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 分页区域 -->
    <div class="pagination-area">
      <el-pagination
        v-model:current-page="pagination.pageNum"
        v-model:page-size="pagination.pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router'
import { getUsers, createUser, updateUser, deleteUser } from '../../api/permission'

// 搜索表单数据
const searchForm = ref({
  username: '',
  phone: ''
})

// 表格数据
const tableData = ref([])
const total = ref(0)

// 分页参数
const pagination = ref({
  pageNum: 1,
  pageSize: 10
})

// 对话框相关数据
const dialogVisible = ref(false)
const dialogType = ref('add')
const userFormRef = ref(null)
const userForm = ref({
  username: '',
  phone: '',
  email: '',
  password: '',
  role: 'user'
})

const isAdmin = ref(false)

// 处理角色变化
const handleRoleChange = (val) => {
  userForm.value.role = val ? 'admin' : 'user'
}

// 表单验证规则
const userFormRules = ref({
  username: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: dialogType.value === 'add', message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  role: [
    { required: false, message: '请选择角色', trigger: 'change' }
  ]
})

// 获取用户列表数据
const getUserList = async () => {
  try {
    const params = {
      ...searchForm.value,
      ...pagination.value
    }
    const response = await getUsers(params)
    if (response.data.code === 200) {
      tableData.value = response.data.data.list
      total.value = response.data.data.total
    } else {
      ElMessage.error(response.data.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  }
}

// 搜索
const handleSearch = () => {
  pagination.value.pageNum = 1
  getUserList()
}

// 重置
const handleReset = () => {
  searchForm.value = {
    username: '',
    phone: ''
  }
  pagination.value = {
    pageNum: 1,
    pageSize: 10
  }
  getUserList()
}

// 分页大小改变
const handleSizeChange = (val) => {
  pagination.value.pageSize = val
  getUserList()
}

// 页码改变
const handleCurrentChange = (val) => {
  pagination.value.pageNum = val
  getUserList()
}

// 打开新增用户对话框
const handleAdd = () => {
  dialogType.value = 'add'
  userForm.value = {
    username: '',
    phone: '',
    email: '',
    password: '',
    role: 'user'
  }
  isAdmin.value = false
  dialogVisible.value = true
}

// 编辑用户
const handleEdit = (row) => {
  dialogType.value = 'edit'
  userForm.value = {
    id: row.id,
    username: row.username,
    phone: row.phone,
    email: row.email,
    password: '', // 编辑时不需要填写密码
    role: row.role
  }
  isAdmin.value = row.role === 'admin'
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${row.username}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await deleteUser(row.id)
    if (response.data.code === 200) {
      ElMessage.success('删除成功')
      getUserList()
    } else {
      ElMessage.error(response.data.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
const submitForm = async () => {
  if (!userFormRef.value) return
  
  await userFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const submitData = { ...userForm.value }
        if (dialogType.value === 'edit' && !userForm.value.password) {
          delete submitData.password // 编辑时，如果密码为空则不提交密码字段
        }

        let response
        if (dialogType.value === 'add') {
          response = await createUser(submitData)
        } else {
          response = await updateUser(userForm.value.id, submitData)
        }

        if (response.data.code === 200) {
          ElMessage.success(dialogType.value === 'add' ? '新增用户成功' : '更新用户成功')
          dialogVisible.value = false
          getUserList()
        } else {
          ElMessage.error(response.data.message || (dialogType.value === 'add' ? '新增用户失败' : '更新用户失败'))
        }
      } catch (error) {
        console.error(dialogType.value === 'add' ? '新增用户失败:' : '更新用户失败:', error)
        ElMessage.error(dialogType.value === 'add' ? '新增用户失败' : '更新用户失败')
      }
    }
  })
}

// 监听路由参数变化
const route = useRoute()

// 处理URL参数中的editUserId
const handleEditUserId = async () => {
  const editUserId = route.query.editUserId
  if (editUserId) {
    // 查找当前用户数据
    const userToEdit = tableData.value.find(user => user.id === editUserId)
    if (userToEdit) {
      handleEdit(userToEdit)
    }
  }
}

onMounted(() => {
  getUserList()
  handleEditUserId()
})
</script>

<style scoped>
.user-list-container {
  padding: 20px;
}

.search-area {
  margin-bottom: 20px;
}

.operation-area {
  margin-bottom: 20px;
}

.pagination-area {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
