<template>
  <el-header class="app-header" height="60px">
    <!-- 左侧区域 -->
    <div class="header-left">
      <!-- 侧边栏切换按钮 -->
      <el-button 
        type="text" 
        class="sidebar-toggle"
        @click="$emit('toggle-sidebar')"
      >
        <el-icon size="18">
          <Expand v-if="collapsed" />
          <Fold v-else />
        </el-icon>
      </el-button>
      
      <!-- 应用Logo和标题 -->
      <div class="app-brand">
        <el-icon class="brand-icon" size="24">
          <Monitor />
        </el-icon>
        <span class="brand-title">运维管理系统</span>
      </div>
    </div>
    
    <!-- 中间区域 - 空白区域 -->
    <div class="header-center">
      <!-- 保留空白区域用于居中布局 -->
    </div>
    
    <!-- 右侧区域 -->
    <div class="header-right">
      <!-- 用户下拉菜单 -->
      <el-dropdown @command="handleCommand" class="user-dropdown">
        <div class="user-info">
          <el-avatar :size="32" class="user-avatar">
            <el-icon><User /></el-icon>
          </el-avatar>
          <span class="username">{{ userInfo.username }}</span>
          <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="logout">
              <el-icon><SwitchButton /></el-icon>
              <span>退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </el-header>
</template>

<script>
import { ref } from 'vue'
import {
  Expand,
  Fold,
  Monitor,
  User,
  ArrowDown,
  SwitchButton
} from '@element-plus/icons-vue'

export default {
  name: 'AppHeader',
  components: {
    Expand,
    Fold,
    Monitor,
    User,
    ArrowDown,
    SwitchButton
  },
  props: {
    userInfo: {
      type: Object,
      default: () => ({})
    },
    collapsed: {
      type: Boolean,
      default: false
    }
  },
  emits: ['toggle-sidebar', 'user-command'],
  setup(props, { emit }) {
    const handleCommand = (command) => {
      emit('user-command', command)
    }

    return {
      handleCommand
    }
  }
}
</script>

<style scoped>
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sidebar-toggle {
  padding: 8px;
  color: #606266;
  transition: all 0.3s;
}

.sidebar-toggle:hover {
  color: #409eff;
  background-color: #f5f7fa;
}

.app-brand {
  display: flex;
  align-items: center;
  gap: 8px;
}

.brand-icon {
  color: #409eff;
}

.brand-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-center {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}



.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.user-avatar {
  background-color: #409eff;
}

.username {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.dropdown-icon {
  color: #909399;
  font-size: 12px;
  transition: transform 0.3s;
}

.user-dropdown.is-active .dropdown-icon {
  transform: rotate(180deg);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .brand-title {
    display: none;
  }
}

@media (max-width: 480px) {
  .username {
    display: none;
  }
}
</style>
