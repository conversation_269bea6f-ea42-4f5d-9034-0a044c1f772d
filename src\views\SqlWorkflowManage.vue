<template>
  <div class="sql-workflow-container">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">SQL工单搜索</span>
          <el-button 
            type="primary" 
            @click="searchWorkflows"
            :loading="loading"
            :icon="Search"
          >
            搜索
          </el-button>
        </div>
      </template>
      
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :default-value="defaultDateRange"
            style="width: 300px;"
          />
        </el-form-item>
        
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索工单名称、提交人等"
            clearable
            style="width: 250px;"
          />
        </el-form-item>
        

        
        <el-form-item>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计信息 -->
    <el-card class="summary-card" v-if="workflowData.summary">
      <div class="summary-info">
        <div class="summary-item">
          <span class="label">总计找到:</span>
          <span class="value">{{ workflowData.summary.total_found }}</span>
        </div>
        <div class="summary-item">
          <span class="label">待运维审批:</span>
          <span class="value pending">{{ workflowData.summary.pending_ops_approval }}</span>
        </div>
        <div class="summary-item">
          <span class="label">总工单数:</span>
          <span class="value">{{ workflowData.summary.total_count }}</span>
        </div>
        <div class="summary-item">
          <span class="label">过滤后数量:</span>
          <span class="value">{{ workflowData.summary.filtered_count }}</span>
        </div>
      </div>
    </el-card>

    <!-- 工单列表 -->
    <el-card class="workflow-list-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">SQL工单列表</span>
          <div class="header-actions">
            <div class="selection-actions" v-if="pendingWorkflows.length > 0">
              <el-checkbox
                v-model="selectAll"
                @change="handleSelectAll"
                :indeterminate="isIndeterminate"
              >
                全选
              </el-checkbox>
              <span class="selection-info">
                已选择 {{ selectedWorkflows.length }} / {{ pendingWorkflows.length }} 个待审批工单
              </span>
            </div>
            <div class="action-buttons">
              <el-button
                type="success"
                @click="batchApproveSelected"
                :loading="batchLoading"
                :disabled="selectedWorkflows.length === 0"
                :icon="Check"
              >
                批量审批选中项 ({{ selectedWorkflows.length }})
              </el-button>
              <el-button
                @click="searchWorkflows"
                :loading="loading"
                :icon="Refresh"
              >
                刷新
              </el-button>
            </div>
          </div>
        </div>
      </template>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="8" animated />
      </div>

      <div v-else-if="workflowList.length === 0" class="empty-container">
        <el-empty description="暂无数据" />
      </div>

      <div v-else class="workflow-cards-container">
        <el-row :gutter="16">
          <el-col
            v-for="workflow in workflowList"
            :key="workflow.workflow_id"
            :xs="24"
            :sm="12"
            :md="12"
            :lg="8"
            :xl="6"
            class="workflow-card-col"
          >
            <div
              class="workflow-status-card"
              :class="getWorkflowCardClass(workflow.status_code)"
            >
              <div class="card-header">
                <div class="workflow-info">
                  <div class="workflow-title-row">
                    <span class="workflow-id">工单 #{{ workflow.workflow_id }}</span>
                    <el-checkbox
                      v-if="workflow.status_code === 'PENDING_OPS_APPROVAL'"
                      v-model="selectedWorkflows"
                      :value="workflow.workflow_id"
                      class="workflow-checkbox"
                      @change="handleWorkflowSelect"
                    />
                  </div>
                  <el-tag
                    :type="getStatusType(workflow.status_code)"
                    size="large"
                    class="status-tag"
                  >
                    <el-icon class="status-icon">
                      <component :is="getStatusIcon(workflow.status_code)" />
                    </el-icon>
                    {{ workflow.status || '未知状态' }}
                  </el-tag>
                </div>
                <div class="card-actions">
                  <el-button
                    type="primary"
                    size="small"
                    @click="openWorkflowUrl(workflow.url)"
                    class="view-button"
                  >
                    查看工单
                  </el-button>
                  <el-button
                    v-if="workflow.status_code === 'PENDING_OPS_APPROVAL'"
                    type="success"
                    size="small"
                    @click="approveWorkflow(workflow.workflow_id)"
                    :loading="approvingIds.includes(workflow.workflow_id)"
                    class="approve-button"
                  >
                    审批
                  </el-button>
                </div>
              </div>
              
              <div class="card-content">
                <div class="workflow-details">
                  <div class="info-row">
                    <span class="label">标题:</span>
                    <span class="value" :title="workflow.title">{{ workflow.title }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">提交人:</span>
                    <span class="value">{{ workflow.submitter }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">提交时间:</span>
                    <span class="value">{{ workflow.submit_time }}</span>
                  </div>

                  <div class="info-row" v-if="workflow.description">
                    <span class="label">描述:</span>
                    <span class="value">{{ workflow.description }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 分页 -->
      <div class="pagination-area" v-if="workflowList.length > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Check,
  CircleCheck,
  Clock,
  CircleClose,
  Warning,
  Document
} from '@element-plus/icons-vue'
import { getPendingSqlWorkflows, approveSqlWorkflow, batchApproveSql } from '../api/wiki'

export default {
  name: 'SqlWorkflowManage',
  components: {
    Search,
    Refresh,
    Check,
    CircleCheck,
    Clock,
    CircleClose,
    Warning,
    Document
  },
  setup() {
    const loading = ref(false)
    const batchLoading = ref(false)
    const approvingIds = ref([])
    
    // 搜索表单
    const searchForm = ref({
      search: ''
    })
    
    // 日期范围 - 默认7天内
    const dateRange = ref([])
    const defaultDateRange = computed(() => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 7)
      return [start, end]
    })
    
    // 分页
    const currentPage = ref(1)
    const pageSize = ref(14)
    const total = ref(0)
    
    // 数据
    const workflowData = ref({})
    const workflowList = ref([])

    // 选择相关
    const selectedWorkflows = ref([])
    const selectAll = ref(false)

    // 计算属性
    const pendingWorkflows = computed(() => {
      return workflowList.value.filter(w => w.status_code === 'PENDING_OPS_APPROVAL')
    })

    const pendingCount = computed(() => {
      return pendingWorkflows.value.length
    })

    const isIndeterminate = computed(() => {
      const selectedCount = selectedWorkflows.value.length
      const totalPending = pendingWorkflows.value.length
      return selectedCount > 0 && selectedCount < totalPending
    })

    const canBatchApprove = computed(() => {
      return pendingCount.value > 0 && !batchLoading.value
    })

    // 初始化日期范围
    const initDateRange = () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 7)
      dateRange.value = [
        start.toISOString().split('T')[0],
        end.toISOString().split('T')[0]
      ]
    }

    // 搜索工单
    const searchWorkflows = async () => {
      loading.value = true
      try {
        const params = {
          limit: pageSize.value,
          offset: (currentPage.value - 1) * pageSize.value,
          debug: false
        }

        // 添加搜索条件
        if (searchForm.value.search) params.search = searchForm.value.search

        // 添加日期范围
        if (dateRange.value && dateRange.value.length === 2) {
          params.start_date = dateRange.value[0]
          params.end_date = dateRange.value[1]
        }

        const response = await getPendingSqlWorkflows(params)

        if (response.data.success) {
          workflowData.value = response.data.data
          workflowList.value = response.data.data.workflows || []
          total.value = response.data.data.summary?.filtered_count || 0
          ElMessage.success(response.data.message || '查询成功')
        } else {
          ElMessage.error(response.data.message || '查询失败')
        }
      } catch (error) {
        ElMessage.error('查询失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loading.value = false
      }
    }

    // 重置搜索
    const resetSearch = () => {
      searchForm.value = {
        search: ''
      }
      initDateRange()
      currentPage.value = 1
      searchWorkflows()
    }

    // 单个工单审批
    const approveWorkflow = async (workflowId) => {
      try {
        await ElMessageBox.confirm(
          `确定要审批工单 #${workflowId} 吗？`,
          '确认审批',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        approvingIds.value.push(workflowId)

        const response = await approveSqlWorkflow({
          workflow_id: parseInt(workflowId),
          debug: false
        })

        if (response.data.success) {
          ElMessage.success(response.data.message || '审批成功')
          // 刷新列表
          searchWorkflows()
        } else {
          ElMessage.error(response.data.message || '审批失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('审批失败: ' + (error.response?.data?.message || error.message))
        }
      } finally {
        approvingIds.value = approvingIds.value.filter(id => id !== workflowId)
      }
    }

    // 处理全选
    const handleSelectAll = (checked) => {
      if (checked) {
        selectedWorkflows.value = pendingWorkflows.value.map(w => w.workflow_id)
      } else {
        selectedWorkflows.value = []
      }
    }

    // 处理单个工单选择
    const handleWorkflowSelect = () => {
      const totalPending = pendingWorkflows.value.length
      const selectedCount = selectedWorkflows.value.length

      if (selectedCount === 0) {
        selectAll.value = false
      } else if (selectedCount === totalPending) {
        selectAll.value = true
      } else {
        selectAll.value = false
      }
    }

    // 批量审批选中的工单
    const batchApproveSelected = async () => {
      if (selectedWorkflows.value.length === 0) {
        ElMessage.warning('请先选择要审批的工单')
        return
      }

      const selectedWorkflowsData = workflowList.value.filter(w =>
        selectedWorkflows.value.includes(w.workflow_id) &&
        w.status_code === 'PENDING_OPS_APPROVAL'
      )

      if (selectedWorkflowsData.length === 0) {
        ElMessage.warning('没有选中有效的待审批工单')
        return
      }

      try {
        await ElMessageBox.confirm(
          `确定要批量审批选中的 ${selectedWorkflowsData.length} 个工单吗？`,
          '确认批量审批',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        batchLoading.value = true

        // 提取选中工单的URL
        const sqlUrls = selectedWorkflowsData.map(w => w.url).filter(url => url)

        if (sqlUrls.length === 0) {
          ElMessage.warning('选中的工单没有有效的URL')
          return
        }

        const response = await batchApproveSql({
          sql_urls: sqlUrls,
          debug: false
        })

        if (response.data.success) {
          const { summary } = response.data.data
          ElMessage.success(
            `批量审批完成！成功: ${summary.success_count}, 失败: ${summary.failed_count}, 跳过: ${summary.skipped_count}`
          )

          // 清空选择
          selectedWorkflows.value = []
          selectAll.value = false

          // 刷新列表
          searchWorkflows()
        } else {
          ElMessage.error(response.data.message || '批量审批失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('批量审批失败: ' + (error.response?.data?.message || error.message))
        }
      } finally {
        batchLoading.value = false
      }
    }

    // 打开工单链接
    const openWorkflowUrl = (url) => {
      if (url) {
        window.open(url, '_blank')
      }
    }

    // 获取状态类型
    const getStatusType = (statusCode) => {
      switch (statusCode) {
        case 'PENDING_OPS_APPROVAL':
          return 'warning'
        case 'PENDING_MANAGER_APPROVAL':
          return 'info'
        case 'PENDING_EXECUTION':
          return 'primary'
        case 'COMPLETED_SUCCESSFULLY':
          return 'success'
        case 'SQL_EXECUTION_ERROR':
        case 'WORKFLOW_CANCELLED':
          return 'danger'
        default:
          return 'info'
      }
    }

    // 获取状态图标
    const getStatusIcon = (statusCode) => {
      switch (statusCode) {
        case 'PENDING_OPS_APPROVAL':
        case 'PENDING_MANAGER_APPROVAL':
          return Clock
        case 'PENDING_EXECUTION':
          return Document
        case 'COMPLETED_SUCCESSFULLY':
          return CircleCheck
        case 'SQL_EXECUTION_ERROR':
        case 'WORKFLOW_CANCELLED':
          return CircleClose
        default:
          return Warning
      }
    }

    // 获取卡片样式类
    const getWorkflowCardClass = (statusCode) => {
      switch (statusCode) {
        case 'PENDING_OPS_APPROVAL':
          return 'pending-ops'
        case 'PENDING_MANAGER_APPROVAL':
          return 'pending-manager'
        case 'PENDING_EXECUTION':
          return 'pending-execution'
        case 'COMPLETED_SUCCESSFULLY':
          return 'completed'
        case 'SQL_EXECUTION_ERROR':
        case 'WORKFLOW_CANCELLED':
          return 'error'
        default:
          return 'unknown'
      }
    }

    // 分页处理
    const handleSizeChange = (val) => {
      pageSize.value = val
      currentPage.value = 1
      searchWorkflows()
    }

    const handleCurrentChange = (val) => {
      currentPage.value = val
      searchWorkflows()
    }

    // 初始化
    onMounted(() => {
      initDateRange()
      searchWorkflows()
    })

    return {
      loading,
      batchLoading,
      approvingIds,
      searchForm,
      dateRange,
      defaultDateRange,
      currentPage,
      pageSize,
      total,
      workflowData,
      workflowList,
      selectedWorkflows,
      selectAll,
      pendingWorkflows,
      pendingCount,
      isIndeterminate,
      canBatchApprove,
      searchWorkflows,
      resetSearch,
      approveWorkflow,
      handleSelectAll,
      handleWorkflowSelect,
      batchApproveSelected,
      openWorkflowUrl,
      getStatusType,
      getStatusIcon,
      getWorkflowCardClass,
      handleSizeChange,
      handleCurrentChange
    }
  }
}
</script>

<style scoped>
.sql-workflow-container {
  padding: var(--spacing-lg);
  background: var(--bg-page);
  min-height: calc(100vh - 60px);
}

.search-card, .summary-card, .workflow-list-card {
  margin-bottom: var(--spacing-lg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
}

.search-form {
  margin-top: var(--spacing-sm);
}

.search-form .el-form-item {
  margin-bottom: var(--spacing-sm);
}

.summary-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.summary-info {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
}

.summary-item {
  text-align: center;
}

.summary-item .label {
  display: block;
  font-size: var(--font-size-sm);
  opacity: 0.9;
  margin-bottom: var(--spacing-xs);
}

.summary-item .value {
  display: block;
  font-size: var(--font-size-xxl);
  font-weight: bold;
}

.summary-item .value.pending {
  color: #ffd700;
}

.header-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  align-items: flex-end;
}

.selection-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: var(--font-size-sm);
}

.selection-info {
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
}

.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
}

.loading-container {
  padding: calc(var(--spacing-xl) + var(--spacing-sm));
}

.empty-container {
  padding: calc(var(--spacing-xl) + var(--spacing-sm));
  text-align: center;
}

.workflow-cards-container {
  margin-bottom: var(--spacing-lg);
}

.workflow-card-col {
  margin-bottom: var(--spacing-md);
}

.workflow-status-card {
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-large);
  background: var(--bg-color);
  transition: all var(--transition-base);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.workflow-status-card:hover {
  box-shadow: var(--shadow-dark);
  transform: translateY(-2px);
}

.workflow-status-card.pending-ops {
  border-left: 4px solid var(--warning-color);
}

.workflow-status-card.pending-manager {
  border-left: 4px solid var(--info-color);
}

.workflow-status-card.pending-execution {
  border-left: 4px solid var(--primary-color);
}

.workflow-status-card.completed {
  border-left: 4px solid var(--success-color);
}

.workflow-status-card.error {
  border-left: 4px solid var(--danger-color);
}

.workflow-status-card.unknown {
  border-left: 4px solid var(--text-placeholder);
}

.workflow-status-card .card-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-lighter);
  flex-direction: column;
  align-items: flex-start;
  gap: var(--spacing-sm);
}

.workflow-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.workflow-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.workflow-id {
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.workflow-checkbox {
  margin-left: var(--spacing-sm);
}

.status-tag {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.status-icon {
  font-size: var(--font-size-sm);
}

.card-actions {
  display: flex;
  gap: var(--spacing-sm);
  width: 100%;
}

.view-button, .approve-button {
  flex: 1;
}

.card-content {
  padding: var(--spacing-md);
  flex: 1;
}

.workflow-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.info-row {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xs);
}

.info-row .label {
  color: var(--text-secondary);
  min-width: 60px;
  flex-shrink: 0;
}

.info-row .value {
  color: var(--text-primary);
  word-break: break-all;
  flex: 1;
}

.pagination-area {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-lg);
  padding: var(--spacing-lg) 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sql-workflow-container {
    padding: var(--spacing-sm);
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    width: 100%;
  }

  .summary-info {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .header-actions {
    align-items: stretch;
    width: 100%;
  }

  .selection-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .card-actions {
    flex-direction: column;
  }

  .workflow-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
}
</style>
