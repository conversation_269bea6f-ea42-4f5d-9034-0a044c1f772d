<template>
  <div class="nginx-consistency-container">
    <el-row :gutter="20">
      <!-- 目录和文件列表 -->
      <el-col :span="24">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">Nginx配置目录</span>
              <el-button type="primary" size="small" @click="loadDirectories" class="refresh-btn">
                <el-icon><Refresh /></el-icon>
                刷新目录
              </el-button>
            </div>
          </template>
          <div v-loading="loadingDirectories">
            <el-select
              v-model="selectedDirectory"
              placeholder="请选择配置目录"
              style="width: 100%"
              @change="handleDirectoryChange"
            >
              <el-option
                v-for="dir in directoryData"
                :key="dir.name"
                :label="dir.name"
                :value="dir.name"
              ></el-option>
            </el-select>
          </div>
        </el-card>

        <el-card class="box-card mt-20" v-if="selectedDirectory">
          <template #header>
            <div class="card-header">
              <span>{{ selectedDirectory }} 目录下的配置文件</span>
              <div>
                <el-button type="warning" size="small" @click="compareSelectedFiles" :disabled="selectedFiles.length === 0">选中文件对比</el-button>
                <el-button type="primary" size="small" @click="viewSelectedFilesHistory" :disabled="selectedFiles.length === 0">刷新对比结果</el-button>
                <el-button type="success" size="small" @click="loadFiles">刷新文件列表</el-button>
              </div>
            </div>
          </template>
          <div v-loading="loadingFiles">
            <el-table
              ref="fileTable"
              :data="fileList"
              style="width: 100%"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="name" label="文件名"></el-table-column>
              <el-table-column label="一致性结果" width="120">
                <template #default="scope">
                  <el-tag v-if="scope.row.consistent !== undefined" :type="scope.row.consistent ? 'success' : 'danger'">
                    {{ scope.row.consistent ? '一致' : '不一致' }}
                  </el-tag>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column label="检查时间" width="180">
                <template #default="scope">
                  <span>{{ scope.row.check_time || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="220">
                <template #default="scope">
                  <el-button type="primary" size="small" @click="checkSingleFile(scope.row)">检查</el-button>
                  <el-button type="info" size="small" @click="viewHistoryDetail(scope.row)" :disabled="!scope.row.check_time">详情</el-button>
                  <el-button type="warning" size="small" @click="viewDetailedDiff(scope.row)" :disabled="!scope.row.check_time || scope.row.consistent">查看差异</el-button>
                </template>
              </el-table-column>
            </el-table>
            

          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 查看节点内容对话框 -->
    <el-dialog
      v-model="contentDialogVisible"
      title="配置文件内容"
      width="70%"
    >
      <pre class="config-content">{{ selectedNodeContent }}</pre>
    </el-dialog>

    <!-- 查看历史详情对话框 -->
    <el-dialog
      v-model="historyDetailDialogVisible"
      title="历史检查详情"
      width="70%"
    >
      <div v-if="selectedHistoryItem">
        <el-alert
          :title="selectedHistoryItem.consistent ? '配置一致' : '配置不一致'"
          :type="selectedHistoryItem.consistent ? 'success' : 'error'"
          :description="'文件路径: ' + selectedHistoryItem.file_path"
          show-icon
        ></el-alert>
        
        <div class="mt-20" v-if="selectedHistoryItem.nodes && selectedHistoryItem.nodes.length">
          <h3>节点检查详情</h3>
          <el-table :data="selectedHistoryItem.nodes" style="width: 100%">
            <el-table-column prop="node" label="节点" width="180"></el-table-column>
            <el-table-column prop="consistent" label="一致性">
              <template #default="scope">
                <el-tag :type="scope.row.consistent ? 'success' : 'danger'">
                  {{ scope.row.consistent ? '一致' : '不一致' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180">
              <template #default="scope">
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="viewHistoryNodeContent(scope.row)"
                  :disabled="!scope.row.content"
                >查看内容</el-button>
                <el-button 
                  type="warning" 
                  size="small" 
                  @click="viewNodeDetailedDiff(scope.row)"
                  :disabled="scope.row.consistent || !scope.row.content"
                >查看差异</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
    
    <!-- 配置文件差异对比对话框 -->
    <el-dialog
      v-model="diffDialogVisible"
      title="配置文件差异对比"
      width="90%"
      top="5vh"
      :destroy-on-close="true"
    >
      <config-diff
        :file-path="currentDiffFilePath"
        :servers="diffServers"
        :contents="diffContents"
        :loading="loadingDiff"
      />
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import ConfigDiff from '../components/ConfigDiff.vue'

export default {
  name: 'NginxConsistency',
  components: {
    ConfigDiff,
    Refresh
  },
  setup() {
    // 数据定义
    const directoryData = ref([])
    const fileList = ref([])
    const selectedDirectory = ref('')
    const selectedFiles = ref([])
    const currentFilePath = ref('')
    const contentDialogVisible = ref(false)
    const selectedNodeContent = ref('')
    const historyDetailDialogVisible = ref(false)
    const selectedHistoryItem = ref(null)
    const checkResult = ref(null)
    const historyResults = ref([])
    
    // 差异对比相关
    const diffDialogVisible = ref(false)
    const currentDiffFilePath = ref('')
    const diffServers = ref([])
    const diffContents = ref([])
    const loadingDiff = ref(false)

    // 加载状态
    const loadingDirectories = ref(false)
    const loadingFiles = ref(false)
    const loadingCheck = ref(false)

    // 获取目录列表
    const loadDirectories = async () => {
      loadingDirectories.value = true
      try {
        const response = await axios.get('/api/nginx/nginx_directories', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        })

        if (response.data.success || response.data.code === 200) {
          directoryData.value = response.data.directories.map(dir => ({
            name: dir
          }))
          if (directoryData.value.length === 0) {
            console.warn('获取到的目录列表为空')
          }
        } else {
          ElMessage.error(response.data.message || '获取目录列表失败')
        }
      } catch (error) {
        ElMessage.error('获取目录列表失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loadingDirectories.value = false
      }
    }

    // 处理目录选择变化
    const handleDirectoryChange = () => {
      if (selectedDirectory.value) {
        loadFiles()
      }
    }

    // 获取文件列表
    const loadFiles = async () => {
      if (!selectedDirectory.value) return

      loadingFiles.value = true
      try {
        const response = await axios.post('/api/nginx/nginx_files', {
          directory: selectedDirectory.value
        }, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        })

        if (response.data.success) {
          fileList.value = response.data.files.map(file => ({
            name: file,
            path: `/etc/nginx/${selectedDirectory.value}/${file}`
          }))
          
          // 获取文件列表后，异步调用历史记录接口
          loadFilesHistory(fileList.value.map(file => file.path))
        } else {
          ElMessage.error(response.data.message || '获取文件列表失败')
        }
      } catch (error) {
        ElMessage.error('获取文件列表失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loadingFiles.value = false
      }
    }
    
    // 获取文件列表的历史记录
    const loadFilesHistory = async (filePaths) => {
      if (!filePaths || filePaths.length === 0) return
      
      // 不影响用户操作，不显示全局loading
      try {
        const response = await axios.post('/api/nginx/nginx_consistency/history', {
          file_paths: filePaths
        }, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        })

        if (response.data.code === 200) {
          const results = response.data.results || []
          
          // 更新文件列表中的一致性结果和检查时间
          if (results.length > 0) {
            results.forEach(result => {
              const index = fileList.value.findIndex(item => item.path === result.file_path)
              if (index !== -1) {
                fileList.value[index].consistent = result.consistent
                fileList.value[index].check_time = result.check_time
              }
            })
          }
        }
      } catch (error) {
        console.error('获取历史记录失败:', error)
        // 不显示错误消息，避免干扰用户
      }
    }

    // 处理文件选择变化
    const handleSelectionChange = (selection) => {
      selectedFiles.value = selection
    }

    // 检查单个文件一致性
    const checkSingleFile = async (file) => {
      currentFilePath.value = file.path
      loadingCheck.value = true
      historyResults.value = []

      try {
        const response = await axios.post('/api/nginx/nginx_consistency/file', {
          file_path: file.path
        }, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        })

        if (response.data.code === 200) {
          checkResult.value = response.data
          // 更新文件列表中的一致性结果和检查时间
          const index = fileList.value.findIndex(item => item.path === file.path)
          if (index !== -1) {
            fileList.value[index].consistent = response.data.consistent
            // 使用北京时间
            const now = new Date()
            const year = now.getFullYear()
            const month = String(now.getMonth() + 1).padStart(2, '0')
            const day = String(now.getDate()).padStart(2, '0')
            const hours = String(now.getHours()).padStart(2, '0')
            const minutes = String(now.getMinutes()).padStart(2, '0')
            const seconds = String(now.getSeconds()).padStart(2, '0')
            fileList.value[index].check_time = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
          }
          ElMessage.success('检查完成')
        } else {
          ElMessage.error(response.data.message || '检查失败')
        }
      } catch (error) {
        ElMessage.error('检查失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loadingCheck.value = false
      }
    }

    // 对比选中的文件
    const compareSelectedFiles = async () => {
      if (selectedFiles.value.length === 0) {
        ElMessage.warning('请至少选择一个文件')
        return
      }

      loadingCheck.value = true

      try {
        const filePaths = selectedFiles.value.map(file => file.path)
        const response = await axios.post('/api/nginx/nginx_consistency/compare', {
          file_paths: filePaths
        }, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        })

        if (response.data.code === 200) {
          ElMessage.success(response.data.message || '已接收文件一致性检查请求，正在后台处理中，请稍后查询结果')
        } else {
          ElMessage.error(response.data.message || '文件对比失败')
        }
      } catch (error) {
        ElMessage.error('文件对比失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loadingCheck.value = false
      }
    }

    // 查看选中文件的历史记录
    const viewSelectedFilesHistory = async () => {
      if (selectedFiles.value.length === 0) {
        ElMessage.warning('请至少选择一个文件')
        return
      }

      loadingCheck.value = true
      historyResults.value = []
      checkResult.value = null

      try {
        const filePaths = selectedFiles.value.map(file => file.path)
        const response = await axios.post('/api/nginx/nginx_consistency/history', {
          file_paths: filePaths
        }, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        })

        if (response.data.code === 200) {
          // 处理API返回的结果
          historyResults.value = response.data.results || []
          
          // 处理未找到的文件
          if (response.data.not_found_files && response.data.not_found_files.length > 0) {
            ElMessage.warning(`有${response.data.not_found_files.length}个文件未找到历史记录`)
          }
          
          // 更新文件列表中的一致性结果和检查时间
          if (historyResults.value.length > 0) {
            historyResults.value.forEach(result => {
              const index = fileList.value.findIndex(item => item.path === result.file_path)
              if (index !== -1) {
                fileList.value[index].consistent = result.consistent
                fileList.value[index].check_time = result.check_time
              }
            })
            ElMessage.success('历史记录加载完成')
          } else {
            ElMessage.info('没有找到历史记录')
          }
        } else {
          ElMessage.error(response.data.message || '获取历史记录失败')
        }
      } catch (error) {
        ElMessage.error('获取历史记录失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loadingCheck.value = false
      }
    }

    // 加载历史记录
    const loadHistory = async (filePaths) => {
      loadingCheck.value = true
      checkResult.value = null

      try {
        const payload = {}
        if (Array.isArray(filePaths)) {
          payload.file_paths = filePaths
        } else if (currentFilePath.value) {
          payload.file_path = currentFilePath.value
        } else {
          ElMessage.warning('请先选择文件')
          loadingCheck.value = false
          return
        }

        const response = await axios.post('/api/nginx/nginx_consistency/history', payload, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        })

        if (response.data.code === 200) {
          historyResults.value = response.data.results || []
          if (historyResults.value.length === 0) {
            ElMessage.info('没有找到历史记录')
          }
        } else {
          ElMessage.error(response.data.message || '获取历史记录失败')
        }
      } catch (error) {
        ElMessage.error('获取历史记录失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loadingCheck.value = false
      }
    }

    // 查看节点内容
    const viewNodeContent = (node) => {
      selectedNodeContent.value = node.content || '无内容'
      contentDialogVisible.value = true
    }

    // 查看历史详情
    const viewHistoryDetail = async (item) => {
      // 如果是从文件列表点击详情按钮，需要先加载该文件的历史记录
      if (item.path && !item.file_path) {
        loadingCheck.value = true
        try {
          const response = await axios.post('/api/nginx/nginx_consistency/history', {
            file_path: item.path
          }, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (response.data.code === 200 && response.data.results && response.data.results.length > 0) {
            // 使用最新的历史记录
            selectedHistoryItem.value = response.data.results[0]
            historyDetailDialogVisible.value = true
          } else {
            ElMessage.info('没有找到该文件的历史记录')
          }
        } catch (error) {
          ElMessage.error('获取历史记录失败: ' + (error.response?.data?.message || error.message))
        } finally {
          loadingCheck.value = false
        }
      } else {
        // 如果是从历史记录列表点击详情按钮，直接显示
        selectedHistoryItem.value = item
        historyDetailDialogVisible.value = true
      }
    }

    // 查看历史节点内容
    const viewHistoryNodeContent = (node) => {
      selectedNodeContent.value = node.content || '无内容'
      contentDialogVisible.value = true
    }
    
    // 查看文件详细差异
    const viewDetailedDiff = async (file) => {
      if (!file.path || file.consistent) return
      
      loadingDiff.value = true
      currentDiffFilePath.value = file.path
      diffServers.value = []
      diffContents.value = []
      diffDialogVisible.value = true
      
      try {
        const response = await axios.post('/api/nginx/nginx_consistency/file_content', {
          file_path: file.path
        }, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        })
        
        if (response.data.code === 200) {
          // 处理服务器列表和内容
          diffServers.value = response.data.nodes.map(node => node.node)
          diffContents.value = response.data.nodes.map(node => node.content || '')
        } else {
          ElMessage.error(response.data.message || '获取文件内容失败')
        }
      } catch (error) {
        ElMessage.error('获取文件内容失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loadingDiff.value = false
      }
    }
    
    // 查看节点详细差异
    const viewNodeDetailedDiff = (node) => {
      if (node.consistent || !node.content) return
      
      // 如果已经有节点内容和差异信息
      if (selectedHistoryItem.value && selectedHistoryItem.value.nodes) {
        const nodes = selectedHistoryItem.value.nodes
        const nodeContents = nodes.map(n => n.content || '')
        const nodeServers = nodes.map(n => n.node)
        
        currentDiffFilePath.value = selectedHistoryItem.value.file_path
        diffServers.value = nodeServers
        diffContents.value = nodeContents
        diffDialogVisible.value = true
      }
    }

    // 页面加载时获取目录列表
    onMounted(() => {
      loadDirectories()
    })

    return {
      directoryData,
      fileList,
      selectedDirectory,
      selectedFiles,
      checkResult,
      historyResults,
      currentFilePath,
      contentDialogVisible,
      selectedNodeContent,
      historyDetailDialogVisible,
      selectedHistoryItem,
      diffDialogVisible,
      currentDiffFilePath,
      diffServers,
      diffContents,
      loadingDiff,
      loadingDirectories,
      loadingFiles,
      loadingCheck,
      loadDirectories,
      handleDirectoryChange,
      loadFiles,
      handleSelectionChange,
      checkSingleFile,
      compareSelectedFiles,
      viewSelectedFilesHistory,
      loadHistory,
      viewNodeContent,
      viewHistoryDetail,
      viewHistoryNodeContent,
      viewDetailedDiff,
      viewNodeDetailedDiff
    }
  }
}
</script>

<style scoped>
.nginx-consistency-container {
  padding: 20px;
  background-color: #f8f9fa;
  width: 100%;
}

.box-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  width: 100%;
}

.box-card:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
}



.mt-20 {
  margin-top: 20px;
}

.config-content {
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 500px;
  overflow-y: auto;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 6px;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
  border: 1px solid #e0e0e0;
}

.el-table {
  border-radius: 6px;
  overflow: hidden;
  width: 100% !important;
}

.el-select {
  margin-bottom: 10px;
  width: 100%;
}

.el-alert {
  margin-bottom: 15px;
}

.history-results h3 {
  margin-bottom: 15px;
  font-weight: 500;
  color: #303133;
}
</style>