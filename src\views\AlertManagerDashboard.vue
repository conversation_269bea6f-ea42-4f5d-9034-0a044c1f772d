<template>
  <div class="alert-dashboard-container">
    <!-- 页面标题 -->
    <el-card class="header-card">
      <div class="card-header">
        <span class="card-title">告警统计仪表板</span>
        <div class="header-actions">
          <el-button
            @click="loadDashboardData"
            :loading="loading"
            :icon="Refresh"
          >
            刷新数据
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <!-- 仪表板内容 -->
    <div v-else class="dashboard-content">
      <!-- 统计卡片 -->
      <el-row :gutter="24" class="stats-cards">
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="stat-card total-alerts">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Bell /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statsData.total_alerts || 0 }}</div>
                <div class="stat-label">总告警数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="stat-card active-alerts">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statsData.active_alerts || 0 }}</div>
                <div class="stat-label">活跃告警</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="stat-card resolved-alerts">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statsData.resolved_alerts || 0 }}</div>
                <div class="stat-label">已解决</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="stat-card today-alerts">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statsData.today_alerts || 0 }}</div>
                <div class="stat-label">今日告警</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 图表区域 -->
      <el-row :gutter="24" class="charts-row">
        <!-- 告警等级分布 -->
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span class="chart-title">告警等级分布</span>
              </div>
            </template>
            <div class="chart-container">
              <div ref="levelChartRef" class="chart" style="height: 300px;"></div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 项目分类分布 -->
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span class="chart-title">项目分类分布</span>
              </div>
            </template>
            <div class="chart-container">
              <div ref="categoryChartRef" class="chart" style="height: 300px;"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 详细统计表格 -->
      <el-card class="table-card">
        <template #header>
          <div class="chart-header">
            <span class="chart-title">详细统计信息</span>
          </div>
        </template>
        
        <el-tabs v-model="activeTab" type="border-card">
          <el-tab-pane label="告警等级统计" name="level">
            <el-table
              :data="statsData.level_stats || []"
              style="width: 100%"
              stripe
            >
              <el-table-column prop="level" label="告警等级" width="150">
                <template #default="scope">
                  <el-tag :type="getLevelType(scope.row.level)" size="small">
                    {{ getLevelText(scope.row.level) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="count" label="数量" />
              <el-table-column label="占比" width="200">
                <template #default="scope">
                  <el-progress
                    :percentage="getPercentage(scope.row.count, statsData.total_alerts)"
                    :color="getLevelColor(scope.row.level)"
                    :show-text="true"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          
          <el-tab-pane label="项目分类统计" name="category">
            <el-table
              :data="statsData.category_stats || []"
              style="width: 100%"
              stripe
            >
              <el-table-column prop="category" label="项目分类" width="150" />
              <el-table-column prop="count" label="数量" />
              <el-table-column label="占比" width="200">
                <template #default="scope">
                  <el-progress
                    :percentage="getPercentage(scope.row.count, statsData.total_alerts)"
                    :show-text="true"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-card>

      <!-- 快捷操作 -->
      <el-card class="actions-card">
        <template #header>
          <div class="chart-header">
            <span class="chart-title">快捷操作</span>
          </div>
        </template>
        
        <div class="quick-actions">
          <el-button
            type="primary"
            @click="goToAlertList"
            :icon="List"
            size="large"
          >
            查看告警列表
          </el-button>
          <el-button
            type="success"
            @click="goToWebhookConfig"
            :icon="Setting"
            size="large"
          >
            Webhook配置
          </el-button>
          <el-button
            type="warning"
            @click="goToSilenceRules"
            :icon="Mute"
            size="large"
          >
            静默规则管理
          </el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  Bell,
  Warning,
  CircleCheck,
  Clock,
  List,
  Setting,
  Mute
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { getAlertStats } from '../api/alertmanager'

export default {
  name: 'AlertManagerDashboard',
  components: {
    Refresh,
    Bell,
    Warning,
    CircleCheck,
    Clock,
    List,
    Setting,
    Mute
  },
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const statsData = ref({})
    const activeTab = ref('level')
    
    // 图表引用
    const levelChartRef = ref(null)
    const categoryChartRef = ref(null)
    
    // 图表实例
    let levelChart = null
    let categoryChart = null

    // 加载仪表板数据
    const loadDashboardData = async () => {
      loading.value = true
      try {
        const response = await getAlertStats()
        
        if (response.data.success) {
          statsData.value = response.data.data
          // 等待DOM更新后初始化图表
          await nextTick()
          initCharts()
        } else {
          ElMessage.error(response.data.message || '获取统计数据失败')
        }
      } catch (error) {
        ElMessage.error('获取统计数据失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loading.value = false
      }
    }

    // 初始化图表
    const initCharts = () => {
      initLevelChart()
      initCategoryChart()
    }

    // 初始化告警等级分布图表
    const initLevelChart = () => {
      if (!levelChartRef.value || !statsData.value.level_stats) return

      if (levelChart) {
        levelChart.dispose()
      }

      levelChart = echarts.init(levelChartRef.value)

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '告警等级',
            type: 'pie',
            radius: '50%',
            data: statsData.value.level_stats.map(item => ({
              value: item.count,
              name: getLevelText(item.level),
              itemStyle: {
                color: getLevelColor(item.level)
              }
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }

      levelChart.setOption(option)
    }

    // 初始化项目分类分布图表
    const initCategoryChart = () => {
      if (!categoryChartRef.value || !statsData.value.category_stats) return

      if (categoryChart) {
        categoryChart.dispose()
      }

      categoryChart = echarts.init(categoryChartRef.value)

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: statsData.value.category_stats.map(item => item.category),
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '告警数量',
            type: 'bar',
            barWidth: '60%',
            data: statsData.value.category_stats.map(item => ({
              value: item.count,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#83bff6' },
                  { offset: 0.5, color: '#188df0' },
                  { offset: 1, color: '#188df0' }
                ])
              }
            }))
          }
        ]
      }

      categoryChart.setOption(option)
    }

    // 工具函数
    const getLevelType = (level) => {
      switch (level) {
        case 'critical':
          return 'danger'
        case 'warning':
          return 'warning'
        case 'info':
          return 'info'
        default:
          return 'info'
      }
    }

    const getLevelText = (level) => {
      switch (level) {
        case 'critical':
          return '严重'
        case 'warning':
          return '警告'
        case 'info':
          return '信息'
        default:
          return '未知'
      }
    }

    const getLevelColor = (level) => {
      switch (level) {
        case 'critical':
          return '#f56c6c'
        case 'warning':
          return '#e6a23c'
        case 'info':
          return '#909399'
        default:
          return '#909399'
      }
    }

    const getPercentage = (count, total) => {
      if (!total || total === 0) return 0
      return Math.round((count / total) * 100)
    }

    // 导航函数
    const goToAlertList = () => {
      router.push('/alertmanager/list')
    }

    const goToWebhookConfig = () => {
      router.push('/alertmanager/webhook-config')
    }

    const goToSilenceRules = () => {
      router.push('/alertmanager/silence-rules')
    }

    // 窗口大小变化时重新调整图表
    const handleResize = () => {
      if (levelChart) {
        levelChart.resize()
      }
      if (categoryChart) {
        categoryChart.resize()
      }
    }

    // 初始化
    onMounted(() => {
      loadDashboardData()
      window.addEventListener('resize', handleResize)
    })

    // 清理
    onUnmounted(() => {
      if (levelChart) {
        levelChart.dispose()
      }
      if (categoryChart) {
        categoryChart.dispose()
      }
      window.removeEventListener('resize', handleResize)
    })

    return {
      loading,
      statsData,
      activeTab,
      levelChartRef,
      categoryChartRef,
      loadDashboardData,
      getLevelType,
      getLevelText,
      getLevelColor,
      getPercentage,
      goToAlertList,
      goToWebhookConfig,
      goToSilenceRules
    }
  }
}
</script>

<style scoped>
.alert-dashboard-container {
  padding: var(--spacing-lg);
  background: var(--bg-page);
  min-height: calc(100vh - 60px);
}

.header-card {
  margin-bottom: var(--spacing-lg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.loading-container {
  padding: calc(var(--spacing-xl) + var(--spacing-sm));
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

/* 统计卡片样式 */
.stats-cards {
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  height: 120px;
  border-radius: var(--border-radius-large);
  transition: all var(--transition-base);
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-dark);
}

.stat-card.total-alerts {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-card.active-alerts {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.stat-card.resolved-alerts {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.stat-card.today-alerts {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: var(--spacing-lg);
}

.stat-icon {
  font-size: 48px;
  margin-right: var(--spacing-lg);
  opacity: 0.8;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: var(--font-size-xxl);
  font-weight: bold;
  line-height: 1;
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

/* 图表区域样式 */
.charts-row {
  margin-bottom: var(--spacing-lg);
}

.chart-card, .table-card, .actions-card {
  margin-bottom: var(--spacing-lg);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-title {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
}

.chart-container {
  padding: var(--spacing-md) 0;
}

.chart {
  width: 100%;
}

/* 快捷操作样式 */
.quick-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.quick-actions .el-button {
  flex: 1;
  min-width: 200px;
  height: 60px;
  font-size: var(--font-size-base);
  border-radius: var(--border-radius-large);
  transition: all var(--transition-base);
}

.quick-actions .el-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-dark);
}

/* 表格样式增强 */
.el-table {
  border-radius: var(--border-radius-base);
  overflow: hidden;
}

.el-progress {
  width: 100%;
}

/* 标签页样式 */
.el-tabs {
  margin-top: var(--spacing-md);
}

.el-tabs__content {
  padding: var(--spacing-md) 0;
}

/* 按钮样式增强 */
.el-button {
  border-radius: var(--border-radius-base);
  transition: all var(--transition-base);
}

.el-button--primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border: none;
}

.el-button--success {
  background: linear-gradient(135deg, var(--success-color), #51cf66);
  border: none;
}

.el-button--warning {
  background: linear-gradient(135deg, var(--warning-color), #ffd43b);
  border: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .alert-dashboard-container {
    padding: var(--spacing-sm);
  }

  .card-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .stat-card {
    height: 100px;
  }

  .stat-content {
    padding: var(--spacing-md);
  }

  .stat-icon {
    font-size: 36px;
    margin-right: var(--spacing-md);
  }

  .stat-value {
    font-size: var(--font-size-xl);
  }

  .quick-actions {
    flex-direction: column;
  }

  .quick-actions .el-button {
    min-width: auto;
    width: 100%;
  }

  .chart {
    height: 250px !important;
  }
}

@media (max-width: 480px) {
  .stats-cards .el-col {
    margin-bottom: var(--spacing-md);
  }

  .stat-card {
    height: 80px;
  }

  .stat-content {
    padding: var(--spacing-sm);
  }

  .stat-icon {
    font-size: 24px;
    margin-right: var(--spacing-sm);
  }

  .stat-value {
    font-size: var(--font-size-lg);
  }

  .stat-label {
    font-size: var(--font-size-xs);
  }

  .chart {
    height: 200px !important;
  }

  .quick-actions .el-button {
    height: 50px;
    font-size: var(--font-size-sm);
  }
}
</style>
