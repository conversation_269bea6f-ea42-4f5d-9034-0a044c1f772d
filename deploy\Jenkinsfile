def getuser() {
    wrap([$class: 'BuildUser']) {
        buildName "#${BUILD_NUMBER}-${BUILD_USER}"}
        buildDescription "${branch}-${idcip}"
}

def imageTag() {
    def date = new Date()
    return date.format('yyyyMMddHHmm')
}

pipeline {
    agent any
    parameters {
        choice(
            choices: ['***********','**********'], 
            description: '环境', 
            name: 'idcip'
        )
        gitParameter(
            name: 'branch',
            type: 'PT_BRANCH',
            defaultValue: 'main',
            branchFilter: '.*',
            quickFilterEnabled: false,
            selectedValue: 'NONE',
            sortMode: 'NONE',
            tagFilter: '*'
        )
    }
    environment {
        DOCKER_IMAGE = '************:20080/uat/iyunwei-vue'
        DOCKER_TAG = imageTag()
        def user = getuser()
    }
    stages {
        stage('Checkout') {
            steps {
                checkout([$class: 'GitSCM',
                    branches: [[name: '$branch']],
                    doGenerateSubmoduleConfigurations: false,
                    extensions: [],
                    submoduleCfg: [],
                    userRemoteConfigs: [[credentialsId: 'ed875d3f-5c81-49db-a050-4c33c352443c', url: "http://***********:58880/guoyabin/iyunwei-vue.git"]]
                ])
            }
        }

        stage('Build with Docker') {
            steps {
                script {
                    sh "cp $WORKSPACE/deploy/Dockerfile $WORKSPACE/"
                    docker.withRegistry('http://************:20080', '9be21042-8835-494b-acba-990fb1ad3170') {
                        def dockerImage = docker.build("${DOCKER_IMAGE}:${DOCKER_TAG}", "$WORKSPACE/")
                        dockerImage.push()
                    }
                }
            }
        }

        stage('Kubernetes Deploy'){
            steps{
                sh """
                    sed -i 's/IMAGE_TAG/${DOCKER_TAG}/g' \$WORKSPACE/deploy/deployment.yaml
                    ssh root@${idcip} "mkdir -p /www/service/iyunwei-vue/"
                    rsync -aqz $WORKSPACE/deploy/*yaml root@${idcip}:/www/service/iyunwei-vue/
                    ssh root@${idcip} "kubectl apply -f /www/service/iyunwei-vue/deployment.yaml"
                """
            }
        }
    }
}