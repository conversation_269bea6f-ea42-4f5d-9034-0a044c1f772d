<template>
  <div id="app">
    <router-view></router-view>
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style lang="scss">
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  background-color: #f5f7fa;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

// 全局样式增强
* {
  box-sizing: border-box;
}

// Element Plus 容器样式修复
.el-container {
  width: 100%;
}

.el-header {
  padding: 0;
}

.el-aside {
  overflow: visible;
}

.el-main {
  padding: 0;
}
</style>