<template>
  <el-aside 
    :width="sidebarWidth" 
    class="app-sidebar"
    :class="{ 'is-collapsed': collapsed }"
  >
    <div class="sidebar-content">
      <!-- 菜单区域 -->
      <el-menu
        :default-active="activeMenu"
        :collapse="collapsed"
        :unique-opened="true"
        class="sidebar-menu"
        @select="handleSelect"
      >
        <template v-for="menu in menuList" :key="menu.id">
          <!-- 有子菜单的情况 -->
          <el-sub-menu 
            v-if="menu.children && menu.children.length" 
            :index="menu.id"
            class="menu-item-group"
          >
            <template #title>
              <el-icon class="menu-icon">
                <component :is="getMenuIcon(menu)" />
              </el-icon>
              <span class="menu-title">{{ menu.title }}</span>
            </template>
            <el-menu-item 
              v-for="subMenu in menu.children" 
              :key="subMenu.id" 
              :index="subMenu.id"
              class="sub-menu-item"
            >
              <el-icon class="submenu-icon">
                <component :is="getMenuIcon(subMenu)" />
              </el-icon>
              <span class="submenu-title">{{ subMenu.title }}</span>
            </el-menu-item>
          </el-sub-menu>
          
          <!-- 无子菜单的情况 -->
          <el-menu-item 
            v-else 
            :index="menu.id"
            class="menu-item"
          >
            <el-icon class="menu-icon">
              <component :is="getMenuIcon(menu)" />
            </el-icon>
            <span class="menu-title">{{ menu.title }}</span>
          </el-menu-item>
        </template>
      </el-menu>
    </div>
    
    <!-- 底部折叠按钮 -->
    <div class="sidebar-footer" v-if="!collapsed">
      <div class="footer-info">
        <el-text size="small" type="info">
          运维管理系统 v1.0
        </el-text>
      </div>
    </div>
  </el-aside>
</template>

<script>
import { computed } from 'vue'
import {
  Monitor,
  Setting,
  DataBoard,
  Link,
  Lock,
  Bell,
  Document,
  User,
  Tools,
  Switch,
  Files,
  Warning,
  Connection,
  Key,
  Mute
} from '@element-plus/icons-vue'

export default {
  name: 'AppSidebar',
  components: {
    Monitor,
    Setting,
    DataBoard,
    Link,
    Lock,
    Bell,
    Document,
    User,
    Tools,
    Switch,
    Files,
    Warning,
    Connection,
    Key,
    Mute
  },
  props: {
    menuList: {
      type: Array,
      default: () => []
    },
    activeMenu: {
      type: String,
      default: ''
    },
    collapsed: {
      type: Boolean,
      default: false
    }
  },
  emits: ['menu-select'],
  setup(props, { emit }) {
    const sidebarWidth = computed(() => {
      return props.collapsed ? '64px' : '240px'
    })
    
    const handleSelect = (index) => {
      emit('menu-select', index)
    }
    
    // 根据菜单标题或路径返回对应的图标
    const getMenuIcon = (menu) => {
      const title = menu.title?.toLowerCase() || ''
      const path = menu.path?.toLowerCase() || ''
      
      // 根据菜单标题或路径匹配图标
      if (title.includes('蓝绿') || path.includes('bluegreen')) return 'Switch'
      if (title.includes('wiki') || path.includes('wiki')) return 'Document'
      if (title.includes('日志') || title.includes('告警') || path.includes('logalert')) return 'Warning'
      if (title.includes('redis') || path.includes('redis')) return 'DataBoard'
      if (title.includes('链接') || title.includes('管理') || path.includes('links')) return 'Link'
      if (title.includes('fail2ban') || path.includes('fail2ban')) return 'Lock'
      if (title.includes('系统') || path.includes('system')) return 'Setting'
      if (title.includes('nginx') || path.includes('nginx')) return 'Tools'
      if (title.includes('数据库') || path.includes('database')) return 'DataBoard'
      if (title.includes('用户') || path.includes('user')) return 'User'
      if (title.includes('同步') || path.includes('sync')) return 'Connection'
      if (title.includes('oracle') || path.includes('oracle')) return 'DataBoard'
      if (title.includes('一致性') || path.includes('consistency')) return 'Files'
      if (title.includes('访问') || path.includes('access')) return 'Key'
      
      // 默认图标
      return 'Monitor'
    }
    
    return {
      sidebarWidth,
      handleSelect,
      getMenuIcon
    }
  }
}
</script>

<style scoped>
.app-sidebar {
  background: #001529;
  transition: width 0.3s ease;
  overflow: hidden;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  height: 100%;
  position: relative;
}

.sidebar-content {
  height: calc(100% - 60px);
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar-content::-webkit-scrollbar {
  width: 4px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.sidebar-menu {
  border-right: none;
  background: transparent;
  width: 100%;
}

.sidebar-menu :deep(.el-menu-item),
.sidebar-menu :deep(.el-sub-menu__title) {
  color: rgba(255, 255, 255, 0.85);
  border-radius: 6px;
  margin: 4px 12px;
  width: calc(100% - 24px);
  height: 48px;
  line-height: 48px;
  transition: all 0.3s;
  position: relative;
  overflow: visible;
}

/* 确保子菜单标题有足够空间显示下拉箭头 */
.sidebar-menu :deep(.el-sub-menu__title) {
  padding-right: 40px !important;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

/* 下拉箭头样式优化 */
.sidebar-menu :deep(.el-sub-menu__icon-arrow) {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  transition: transform 0.3s;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.sidebar-menu :deep(.el-sub-menu.is-opened .el-sub-menu__icon-arrow) {
  transform: translateY(-50%) rotate(180deg);
  color: #1890ff;
}

.sidebar-menu :deep(.el-menu-item:hover),
.sidebar-menu :deep(.el-sub-menu__title:hover) {
  color: #fff;
  background-color: rgba(24, 144, 255, 0.2);
}

.sidebar-menu :deep(.el-menu-item.is-active) {
  color: #fff;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.sidebar-menu :deep(.el-sub-menu.is-active > .el-sub-menu__title) {
  color: #1890ff;
}

.sidebar-menu :deep(.el-sub-menu .el-menu) {
  background: rgba(0, 0, 0, 0.2) !important;
}

.sidebar-menu :deep(.el-sub-menu__title) {
  border-left: 3px solid transparent;
  transition: all 0.3s;
}

.sidebar-menu :deep(.el-sub-menu.is-opened > .el-sub-menu__title) {
  border-left-color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1) !important;
}

.sidebar-menu :deep(.el-menu-item-group__title) {
  padding: 0;
}

.menu-icon,
.submenu-icon {
  margin-right: 12px;
  font-size: 14px;
  width: 16px;
  text-align: center;
  opacity: 0.8;
}

.menu-title,
.submenu-title {
  font-size: 14px;
  font-weight: 500;
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 确保菜单项内容不会与箭头重叠 */
.sidebar-menu :deep(.el-sub-menu__title .menu-title) {
  margin-right: 8px;
}

.sub-menu-item {
  padding-left: 48px !important;
}

.sidebar-menu :deep(.el-sub-menu .el-menu-item) {
  color: rgba(255, 255, 255, 0.85) !important;
  background: rgba(0, 21, 41, 0.3) !important;
  margin: 2px 12px;
  border-radius: 4px;
  height: 40px;
  line-height: 40px;
  border-left: 3px solid transparent;
  transition: all 0.3s;
}

.sidebar-menu :deep(.el-sub-menu .el-menu-item:hover) {
  color: #fff !important;
  background-color: rgba(24, 144, 255, 0.2) !important;
  border-left-color: #1890ff;
}

.sidebar-menu :deep(.el-sub-menu .el-menu-item.is-active) {
  color: #fff !important;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.3), rgba(9, 109, 217, 0.3)) !important;
  border-left-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.sidebar-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.2);
}

.footer-info {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

/* 折叠状态样式 */
.is-collapsed .sidebar-menu :deep(.el-menu-item),
.is-collapsed .sidebar-menu :deep(.el-sub-menu__title) {
  margin: 4px 8px;
  width: calc(100% - 16px);
  text-align: center;
  padding: 0 !important;
  justify-content: center;
}

.is-collapsed .menu-icon,
.is-collapsed .submenu-icon {
  margin-right: 0;
}

/* 折叠状态下隐藏下拉箭头 */
.is-collapsed .sidebar-menu :deep(.el-sub-menu__icon-arrow) {
  display: none;
}

/* 折叠状态下的子菜单标题 */
.is-collapsed .sidebar-menu :deep(.el-sub-menu__title) {
  padding-right: 0 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-sidebar {
    position: fixed;
    left: 0;
    top: 60px;
    height: calc(100vh - 60px);
    z-index: 999;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .app-sidebar:not(.is-collapsed) {
    transform: translateX(0);
  }
}
</style>
