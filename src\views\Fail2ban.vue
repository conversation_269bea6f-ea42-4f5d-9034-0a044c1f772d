<template>
  <div class="fail2ban-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span class="card-title">IP封禁管理</span>
              <el-button type="info" @click="refreshData" :loading="loading" class="refresh-btn">
                <el-icon><Refresh /></el-icon>
                刷新数据
              </el-button>
            </div>
          </template>
          <div class="alert-form">
            <el-form :inline="true" @submit.prevent="banIP">
              <el-form-item>
                <el-input
                  v-model="ipToBan"
                  placeholder="输入要手动封禁的IP地址"
                  style="width: 300px;"
                />
              </el-form-item>
              <el-form-item>
                <el-select
                  v-model="selectedJail"
                  placeholder="选择监狱类型"
                  style="width: 200px;"
                >
                  <el-option
                    v-for="jail in jailList"
                    :key="jail"
                    :label="jail"
                    :value="jail"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="banIP">
                  <el-icon><Plus /></el-icon>封禁
                </el-button>
                <el-button type="info" @click="refreshData" :loading="loading">
                  <el-icon><Refresh /></el-icon>刷新
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <div class="alert-list">
            <el-table
              v-loading="loading"
              :data="bannedIPs"
              style="width: 100%"
            >
              <el-table-column prop="ip" label="IP地址" width="140">
                <template #default="scope">
                  <el-tag size="small" effect="plain">{{ scope.row.ip }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="IP归属地" width="180">
                <template #default="scope">
                  <div v-if="scope.row.ipLocation">
                    {{ scope.row.ipLocation.country }} {{ scope.row.ipLocation.regionName }} {{ scope.row.ipLocation.city }}
                  </div>
                  <div v-else-if="scope.row.ipLocationLoading">
                    <el-icon class="is-loading"><Loading /></el-icon> 加载中...
                  </div>
                  <div v-else>
                    <el-button size="small" @click="getIpLocation(scope.row)" type="info">
                      <el-icon><Location /></el-icon>获取归属地
                    </el-button>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="jail" label="监狱类型" width="120">
                <template #default="scope">
                  <el-tag type="warning">{{ scope.row.jail }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="ban_time" label="封禁时间" width="180" />
              <el-table-column label="封禁天数" width="100">
                <template #default="scope">
                  <el-tag type="info" effect="plain">{{ calculateBanDays(scope.row.ban_days) }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="解封时间" width="180">
                <template #default="scope">
                  {{ calculateUnbanTime(scope.row) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center">
                <template #default="scope">
                  <el-button 
                    type="danger" 
                    size="small" 
                    @click="unbanIP(scope.row.ip, scope.row.jail)"
                  >
                    <el-icon><Delete /></el-icon>解封
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAllBannedIPs, banIP as banIPApi, unbanIP as unbanIPApi } from '../api/fail2ban'
import axios from 'axios'
import { Loading, Plus, Delete, Location, Refresh } from '@element-plus/icons-vue'

export default {
  name: 'Fail2ban',
  components: {
    Plus,
    Delete,
    Location,
    Loading,
    Refresh
  },
  setup() {
    const loading = ref(false)
    const bannedIPs = ref([])
    const ipToBan = ref('')
    const jailList = ref([])
    const selectedJail = ref('')

    const getJails = async () => {
      loading.value = true
      try {
        const {data} = await getAllBannedIPs()
        bannedIPs.value = data.data.banned_ips.map(ip => ({
          ...ip,
          ipLocation: null,
          ipLocationLoading: false
        }))
        jailList.value = data.data.jails || []
      } catch (error) {
        ElMessage.error('获取封禁IP列表失败：' + (error.response?.data?.message || error.message))
      } finally {
        loading.value = false
      }
    }

    const refreshData = async () => {
      await getJails()
    }

    const getIpLocation = async (ip) => {
      ip.ipLocationLoading = true
      try {
        const response = await axios.get(`http://ip-api.com/json/${ip.ip}?lang=zh-CN`, {
          headers: {
            'Accept': 'application/json'
          }
        })
        ip.ipLocation = response.data
      } catch (error) {
        ElMessage.error('获取IP归属地失败：' + error.message)
        ip.ipLocation = { country: '未知', regionName: '', city: '' }
      } finally {
        ip.ipLocationLoading = false
      }
    }
    
    const calculateBanDays = (banDays) => {
      if (!banDays) return '永久'
      return `${banDays}天`
    }
    
    const calculateUnbanTime = (ip) => {
      return ip.unban_time || '未知'
    }

    const unbanIP = async (ip, jailName) => {
      try {
        await ElMessageBox.confirm(`确定要解除IP ${ip} 的封禁吗？`, '确认操作', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        loading.value = true
        await unbanIPApi({ ip, jail: jailName })
        ElMessage.success(`IP ${ip} 已解除封禁`)
        
        await getJails()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('解除封禁失败：' + (error.response?.data?.message || error.message))
        }
      } finally {
        loading.value = false
      }
    }

    const banIP = async () => {
      if (!ipToBan.value) {
        ElMessage.warning('请输入要封禁的IP地址')
        return
      }
      if (!selectedJail.value) {
        ElMessage.warning('请选择监狱类型')
        return
      }
      
      try {
        loading.value = true
        await banIPApi({ ip: ipToBan.value, jail: selectedJail.value })
        ElMessage.success(`IP ${ipToBan.value} 已封禁`)
        ipToBan.value = ''
        selectedJail.value = ''
        
        await getJails()
      } catch (error) {
        ElMessage.error('封禁IP失败：' + (error.response?.data?.message || error.message))
      } finally {
        loading.value = false
      }
    }

    onMounted(() => {
      refreshData()
    })

    return {
      loading,
      bannedIPs,
      ipToBan,
      jailList,
      selectedJail,
      refreshData,
      unbanIP,
      banIP,
      getIpLocation,
      calculateBanDays,
      calculateUnbanTime
    }
  }
}
</script>

<style scoped>
.fail2ban-container {
  padding: 20px;
}



.alert-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.alert-list {
  margin-top: 20px;
}
</style>