<template>
  <div class="wiki-status-container">
    <el-card class="operation-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">操作面板</span>
        </div>
      </template>
      
      <!-- 操作区域 -->
      <el-row :gutter="20" class="operation-row">
        <el-col :span="16">
          <el-input
            v-model="wikiUrl"
            placeholder="请输入Wiki页面URL（留空则自动搜索最新上线工单）"
            clearable
          >
            <template #prepend>Wiki URL</template>
          </el-input>
        </el-col>
        <el-col :span="8">
          <el-button 
            type="primary" 
            @click="analyzeWiki"
            :loading="loading"
            :icon="Search"
          >
            分析Wiki页面
          </el-button>
          <el-button 
            @click="extractLinks"
            :loading="loading"
            :icon="Link"
          >
            仅提取链接
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 结果展示区域 -->
    <el-card v-if="analysisResult" class="result-card">
      <template #header>
        <div class="card-header">
          <span>分析结果</span>
          <div class="header-actions">
            <el-tag :type="getOverallStatusType()">
              总计: {{ analysisResult.status_results?.summary?.total_count || 0 }} 个链接
            </el-tag>
            <el-button
              v-if="analysisResult.wiki_urls?.length"
              type="success"
              size="small"
              @click="openWikiUrls"
              :icon="Document"
              class="wiki-button"
            >
              查看Wiki原文 ({{ analysisResult.wiki_count || analysisResult.wiki_urls.length }})
            </el-button>
          </div>
        </div>
      </template>

      <!-- 摘要信息 -->
      <div class="summary-section">
        <div class="summary-header">
          <h2>上线审批状态总览</h2>
          <el-tag
            :type="getOverallStatusType()"
            size="large"
            class="overall-status-tag"
          >
            {{ getOverallStatusText() }}
          </el-tag>
        </div>
        <el-row :gutter="20" class="summary-cards">
          <el-col :span="6">
            <div class="summary-card apollo-card">
              <div class="card-icon">
                <el-icon size="24"><Setting /></el-icon>
              </div>
              <div class="card-content">
                <div class="card-value">{{ analysisResult.status_results?.summary?.apollo_count || 0 }}</div>
                <div class="card-title">Apollo配置</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-card git-card">
              <div class="card-icon">
                <el-icon size="24"><Connection /></el-icon>
              </div>
              <div class="card-content">
                <div class="card-value">{{ analysisResult.status_results?.summary?.git_count || 0 }}</div>
                <div class="card-title">Git合并请求</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-card sql-card">
              <div class="card-icon">
                <el-icon size="24"><DataBoard /></el-icon>
              </div>
              <div class="card-content">
                <div class="card-value">{{ analysisResult.status_results?.summary?.sql_count || 0 }}</div>
                <div class="card-title">SQL工单</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-card total-card">
              <div class="card-icon">
                <el-icon size="24"><List /></el-icon>
              </div>
              <div class="card-content">
                <div class="card-value">{{ analysisResult.status_results?.summary?.total_count || 0 }}</div>
                <div class="card-title">总计</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- Apollo结果 -->
      <div v-if="analysisResult.status_results?.apollo_results?.length" class="result-section">
        <div class="section-header">
          <h3><el-icon><Setting /></el-icon>Apollo配置状态</h3>
          <div class="status-summary">
            <span class="summary-text">
              {{ getApolloStatusSummary() }}
            </span>
          </div>
        </div>
        <div class="apollo-cards-container">
          <el-row :gutter="16">
            <el-col
              v-for="item in analysisResult.status_results.apollo_results"
              :key="item.appid + '_' + item.env + '_' + item.cluster"
              :xs="24"
              :sm="12"
              :md="12"
              :lg="8"
              :xl="6"
              class="apollo-card-col"
            >
              <div
                class="apollo-status-card"
                :class="getApolloCardClass(item.status_code)"
              >
                <div class="card-header">
                  <div class="apollo-info">
                    <span class="apollo-id">{{ item.appid }}</span>
                    <el-tag
                      :type="getStatusType(item.status_code)"
                      size="large"
                      class="status-tag"
                    >
                      <el-icon class="status-icon">
                        <component :is="getStatusIcon(item.status_code)" />
                      </el-icon>
                      {{ item.status || '检查失败' }}
                    </el-tag>
                  </div>
                  <el-button
                    type="primary"
                    size="small"
                    @click="openLink(item.url)"
                    v-if="item.url"
                    class="view-button"
                  >
                    查看配置
                  </el-button>
                </div>
                <div class="card-content">
                  <div class="apollo-details">
                    <div class="info-row">
                      <span class="label">环境:</span>
                      <span class="value">{{ item.env }}</span>
                    </div>
                    <div class="info-row">
                      <span class="label">集群:</span>
                      <span class="value">{{ item.cluster }}</span>
                    </div>
                    <div class="info-row" v-if="item.description">
                      <span class="label">描述:</span>
                      <span class="value">{{ item.description }}</span>
                    </div>
                    <div class="info-row" v-if="item.check_time">
                      <span class="label">检查时间:</span>
                      <span class="value">{{ item.check_time }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- Git结果 -->
      <div v-if="analysisResult.status_results?.git_results?.length" class="result-section">
        <div class="section-header">
          <h3><el-icon><Connection /></el-icon>Git合并请求状态</h3>
          <div class="status-summary">
            <span class="summary-text">
              {{ getGitStatusSummary() }}
            </span>
          </div>
        </div>
        <div class="git-cards-container">
          <el-row :gutter="16">
            <el-col
              v-for="item in analysisResult.status_results.git_results"
              :key="item.url"
              :xs="24"
              :sm="12"
              :md="12"
              :lg="8"
              :xl="6"
              class="git-card-col"
            >
              <div
                class="git-status-card"
                :class="getGitCardClass(item.status_code)"
              >
            <div class="card-header">
              <div class="status-indicator">
                <el-tag
                  :type="getStatusType(item.status_code)"
                  size="large"
                  class="status-tag"
                >
                  <el-icon class="status-icon">
                    <component :is="getStatusIcon(item.status_code)" />
                  </el-icon>
                  {{ item.status }}
                </el-tag>
              </div>
              <el-button
                type="primary"
                size="small"
                @click="openLink(item.url)"
                class="view-button"
              >
                查看详情
              </el-button>
            </div>
            <div class="card-content">
              <h4 class="merge-title">{{ item.title }}</h4>
              <div class="merge-info">
                <div class="info-row">
                  <span class="label">作者:</span>
                  <span class="value">{{ item.author }}</span>
                </div>
                <div class="info-row">
                  <span class="label">分支:</span>
                  <span class="value branch-info">
                    <code>{{ item.source_branch }}</code>
                    <el-icon><Right /></el-icon>
                    <code>{{ item.target_branch }}</code>
                  </span>
                </div>
                <div class="info-row" v-if="item.merged_at">
                  <span class="label">合并时间:</span>
                  <span class="value">{{ item.merged_at }}</span>
                </div>
              </div>
            </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- SQL结果 -->
      <div v-if="analysisResult.status_results?.sql_results?.length" class="result-section">
        <div class="section-header">
          <h3><el-icon><DataBoard /></el-icon>SQL工单状态</h3>
          <div class="header-actions">
            <div class="status-summary">
              <span class="summary-text">
                {{ getSqlStatusSummary() }}
              </span>
            </div>
            <el-button
              type="success"
              size="small"
              @click="batchApproveSqlWorkflows"
              :loading="batchApprovalLoading"
              :disabled="!canBatchApprove"
              class="batch-approve-button"
            >
              <el-icon><CircleCheck /></el-icon>
              一键审批 ({{ getPendingApprovalCount() }})
            </el-button>
          </div>
        </div>
        <div class="sql-cards-container">
          <el-row :gutter="16">
            <el-col
              v-for="item in analysisResult.status_results.sql_results"
              :key="item.workflow_id"
              :xs="24"
              :sm="12"
              :md="12"
              :lg="8"
              :xl="6"
              class="sql-card-col"
            >
              <div
                class="sql-status-card"
                :class="getSqlCardClass(item.status_code)"
              >
            <div class="card-header">
              <div class="workflow-info">
                <span class="workflow-id">工单 #{{ item.workflow_id }}</span>
                <el-tag
                  :type="getStatusType(item.status_code)"
                  size="large"
                  class="status-tag"
                >
                  <el-icon class="status-icon">
                    <component :is="getStatusIcon(item.status_code)" />
                  </el-icon>
                  {{ item.status || '检查失败' }}
                </el-tag>
              </div>
              <el-button
                type="primary"
                size="small"
                @click="openLink(item.url)"
                class="view-button"
              >
                查看工单
              </el-button>
            </div>
            <div class="card-content">
              <div class="sql-info">
                <div class="info-row" v-if="item.description">
                  <span class="label">描述:</span>
                  <span class="value">{{ item.description }}</span>
                </div>
                <div class="info-row suggestion-row" v-if="item.suggestion">
                  <span class="label">建议:</span>
                  <span class="value suggestion">{{ item.suggestion }}</span>
                </div>
              </div>
            </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>

    <!-- 链接提取结果 -->
    <el-card v-if="extractResult" class="result-card">
      <template #header>
        <div class="card-header">
          <span>提取的链接</span>
          <el-button type="primary" size="small" @click="checkExtractedLinks">
            检查状态
          </el-button>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="8" v-if="extractResult.apollo_links?.length">
          <h4>Apollo链接 ({{ extractResult.apollo_links.length }})</h4>
          <el-tag v-for="link in extractResult.apollo_links" :key="link" class="link-tag">
            <el-button type="text" @click="openLink(link)">{{ link }}</el-button>
          </el-tag>
        </el-col>
        <el-col :span="8" v-if="extractResult.git_links?.length">
          <h4>Git链接 ({{ extractResult.git_links.length }})</h4>
          <el-tag v-for="link in extractResult.git_links" :key="link" class="link-tag">
            <el-button type="text" @click="openLink(link)">{{ link }}</el-button>
          </el-tag>
        </el-col>
        <el-col :span="8" v-if="extractResult.sql_links?.length">
          <h4>SQL链接 ({{ extractResult.sql_links.length }})</h4>
          <el-tag v-for="link in extractResult.sql_links" :key="link" class="link-tag">
            <el-button type="text" @click="openLink(link)">{{ link }}</el-button>
          </el-tag>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search,
  Link,
  Setting,
  Connection,
  DataBoard,
  List,
  Right,
  CircleCheck,
  Clock,
  CircleClose,
  Warning,
  Document
} from '@element-plus/icons-vue'
import { analyzeWikiPage, extractWikiLinks, checkLinkStatus, batchApproveSql } from '../api/wiki'

export default {
  name: 'WikiStatus',
  components: {
    Search,
    Link,
    Setting,
    Connection,
    DataBoard,
    List,
    Right,
    CircleCheck,
    Clock,
    CircleClose,
    Warning,
    Document
  },
  setup() {
    const loading = ref(false)
    const batchApprovalLoading = ref(false)
    const wikiUrl = ref('')
    const analysisResult = ref(null)
    const extractResult = ref(null)

    // 分析Wiki页面
    const analyzeWiki = async () => {
      loading.value = true
      try {
        const requestData = wikiUrl.value ? { wiki_url: wikiUrl.value } : {}
        const response = await analyzeWikiPage(requestData)
        
        if (response.data.success) {
          analysisResult.value = response.data.data
          extractResult.value = null // 清空提取结果
          ElMessage.success(response.data.message || '分析完成')
        } else {
          ElMessage.error(response.data.message || '分析失败')
        }
      } catch (error) {
        ElMessage.error('分析失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loading.value = false
      }
    }

    // 提取链接
    const extractLinks = async () => {
      if (!wikiUrl.value) {
        ElMessage.warning('请输入Wiki页面URL')
        return
      }
      
      loading.value = true
      try {
        const response = await extractWikiLinks({ wiki_url: wikiUrl.value })
        
        if (response.data.success) {
          extractResult.value = response.data.data
          analysisResult.value = null // 清空分析结果
          ElMessage.success(response.data.message || '提取链接成功')
        } else {
          ElMessage.error(response.data.message || '提取链接失败')
        }
      } catch (error) {
        ElMessage.error('提取链接失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loading.value = false
      }
    }

    // 检查提取的链接状态
    const checkExtractedLinks = async () => {
      if (!extractResult.value) return
      
      loading.value = true
      try {
        const requestData = {}
        if (extractResult.value.apollo_links?.length) {
          requestData.apollo_links = extractResult.value.apollo_links
        }
        if (extractResult.value.git_links?.length) {
          requestData.git_links = extractResult.value.git_links
        }
        if (extractResult.value.sql_links?.length) {
          requestData.sql_links = extractResult.value.sql_links
        }
        
        const response = await checkLinkStatus(requestData)
        
        if (response.data.success) {
          analysisResult.value = {
            status_results: response.data.data,
            wiki_url: extractResult.value.wiki_url
          }
          ElMessage.success(response.data.message || '状态检查完成')
        } else {
          ElMessage.error(response.data.message || '状态检查失败')
        }
      } catch (error) {
        ElMessage.error('状态检查失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loading.value = false
      }
    }

    // 获取状态类型
    const getStatusType = (statusCode) => {
      const statusMap = {
        'MR_MERGED': 'success',
        'MR_OPEN': 'warning',         // Git开放状态使用黄色
        'MR_OPENED': 'warning',       // Git开放中状态使用黄色
        'COMPLETED_SUCCESSFULLY': 'success',  // SQL工单成功完成状态使用绿色
        'WORKFLOW_COMPLETED': 'success',      // SQL工单已完成状态使用绿色
        'WORKFLOW_FINISH': 'success',         // SQL工单完成状态使用绿色
        'WORKFLOW_SUCCESS': 'success',        // SQL工单成功状态使用绿色
        'COMPLETED': 'success',               // 完成状态使用绿色
        'SUCCESS': 'success',                 // 成功状态使用绿色
        'FINISHED': 'success',                // 结束状态使用绿色
        'PENDING_OPS_APPROVAL': 'warning',   // SQL工单等待运维组审批状态使用黄色
        'PENDING_RELEASE': 'warning',
        'PENDING_EXECUTION': 'warning',
        'NO_PENDING_RELEASE': 'success',     // Apollo无待发布配置状态使用绿色
        'WORKFLOW_CANCELLED': 'info'
      }
      return statusMap[statusCode] || 'danger'
    }

    // 获取整体状态类型
    const getOverallStatusType = () => {
      if (!analysisResult.value?.status_results?.summary) return 'info'
      const total = analysisResult.value.status_results.summary.total_count
      return total > 0 ? 'success' : 'info'
    }

    // 获取整体状态文本
    const getOverallStatusText = () => {
      if (!analysisResult.value?.status_results?.summary) return '未检查'
      const summary = analysisResult.value.status_results.summary
      const total = summary.total_count
      if (total === 0) return '无待处理项目'

      const apollo = summary.apollo_count || 0
      const git = summary.git_count || 0
      const sql = summary.sql_count || 0

      if (apollo + git + sql === total) {
        return '所有项目已完成'
      } else {
        return `${total} 个项目待处理`
      }
    }

    // 获取状态图标
    const getStatusIcon = (statusCode) => {
      const iconMap = {
        'MR_MERGED': 'CircleCheck',
        'MR_OPEN': 'Clock',                       // Git开放状态使用时钟图标
        'MR_OPENED': 'Clock',                     // Git开放中状态使用时钟图标
        'COMPLETED_SUCCESSFULLY': 'CircleCheck',  // SQL工单成功完成状态使用勾选图标
        'WORKFLOW_COMPLETED': 'CircleCheck',      // SQL工单已完成状态使用勾选图标
        'WORKFLOW_FINISH': 'CircleCheck',         // SQL工单完成状态使用勾选图标
        'WORKFLOW_SUCCESS': 'CircleCheck',        // SQL工单成功状态使用勾选图标
        'COMPLETED': 'CircleCheck',               // 完成状态使用勾选图标
        'SUCCESS': 'CircleCheck',                 // 成功状态使用勾选图标
        'FINISHED': 'CircleCheck',                // 结束状态使用勾选图标
        'PENDING_OPS_APPROVAL': 'Clock',          // SQL工单等待运维组审批状态使用时钟图标
        'PENDING_RELEASE': 'Clock',
        'PENDING_EXECUTION': 'Clock',
        'NO_PENDING_RELEASE': 'CircleCheck',      // Apollo无待发布配置状态使用勾选图标
        'WORKFLOW_CANCELLED': 'CircleClose'
      }
      return iconMap[statusCode] || 'Warning'
    }

    // 获取Git状态摘要
    const getGitStatusSummary = () => {
      const gitResults = analysisResult.value?.status_results?.git_results || []
      const merged = gitResults.filter(item => item.status_code === 'MR_MERGED').length
      const open = gitResults.filter(item =>
        item.status_code === 'MR_OPEN' || item.status_code === 'MR_OPENED'
      ).length
      const other = gitResults.length - merged - open

      if (open > 0 && other > 0) {
        return `${merged} 个已合并，${open} 个开放，${other} 个其他状态`
      } else if (open > 0) {
        return `${merged} 个已合并，${open} 个开放`
      } else {
        return `${merged} 个已合并，${gitResults.length - merged} 个待处理`
      }
    }

    // 获取SQL状态摘要
    const getSqlStatusSummary = () => {
      const sqlResults = analysisResult.value?.status_results?.sql_results || []
      const completed = sqlResults.filter(item =>
        item.status_code === 'COMPLETED_SUCCESSFULLY' ||  // SQL工单成功完成
        item.status_code === 'WORKFLOW_COMPLETED' ||
        item.status_code === 'WORKFLOW_FINISH' ||
        item.status_code === 'WORKFLOW_SUCCESS' ||
        item.status_code === 'COMPLETED' ||
        item.status_code === 'SUCCESS' ||
        item.status_code === 'FINISHED'
      ).length
      const pending = sqlResults.filter(item =>
        item.status_code === 'PENDING_OPS_APPROVAL' ||    // 等待运维组审批
        item.status_code === 'PENDING_RELEASE' ||
        item.status_code === 'PENDING_EXECUTION'
      ).length
      const other = sqlResults.length - completed - pending

      if (pending > 0 && other > 0) {
        return `${completed} 个已完成，${pending} 个待审批，${other} 个其他状态`
      } else if (pending > 0) {
        return `${completed} 个已完成，${pending} 个待审批`
      } else {
        return `${completed} 个已完成，${sqlResults.length - completed} 个待处理`
      }
    }

    // 获取Apollo状态摘要
    const getApolloStatusSummary = () => {
      const apolloResults = analysisResult.value?.status_results?.apollo_results || []
      const completed = apolloResults.filter(item =>
        item.status_code === 'MR_MERGED' ||          // 真正合并的算已完成
        item.status_code === 'NO_PENDING_RELEASE'    // 无待发布配置也算已完成
      ).length
      const pending = apolloResults.length - completed
      return `${completed} 个已完成，${pending} 个待处理`
    }

    // 获取Git卡片样式类
    const getGitCardClass = (statusCode) => {
      const classMap = {
        'MR_MERGED': 'status-success',
        'MR_OPEN': 'status-warning',        // Git开放状态使用黄色样式
        'MR_OPENED': 'status-warning',      // Git开放中状态使用黄色样式
        'PENDING_RELEASE': 'status-warning',
        'PENDING_EXECUTION': 'status-warning'
      }
      return classMap[statusCode] || 'status-danger'  // 只有异常情况才使用红色
    }

    // 获取SQL卡片样式类
    const getSqlCardClass = (statusCode) => {
      const classMap = {
        'COMPLETED_SUCCESSFULLY': 'status-success',  // SQL工单成功完成状态使用绿色
        'WORKFLOW_COMPLETED': 'status-success',
        'WORKFLOW_FINISH': 'status-success',
        'WORKFLOW_SUCCESS': 'status-success',
        'COMPLETED': 'status-success',
        'SUCCESS': 'status-success',
        'FINISHED': 'status-success',
        'PENDING_OPS_APPROVAL': 'status-warning',    // SQL工单等待运维组审批状态使用黄色
        'PENDING_RELEASE': 'status-warning',
        'PENDING_EXECUTION': 'status-warning',
        'WORKFLOW_CANCELLED': 'status-danger'
      }
      return classMap[statusCode] || 'status-danger'
    }

    // 获取Apollo卡片样式类
    const getApolloCardClass = (statusCode) => {
      const classMap = {
        'MR_MERGED': 'status-success',
        'PENDING_RELEASE': 'status-warning',
        'PENDING_EXECUTION': 'status-warning',
        'WORKFLOW_CANCELLED': 'status-danger',
        'NO_PENDING_RELEASE': 'status-success'
      }
      return classMap[statusCode] || 'status-danger'
    }

    // 打开链接
    const openLink = (url) => {
      window.open(url, '_blank')
    }

    // 打开Wiki URL
    const openWikiUrls = () => {
      const wikiUrls = analysisResult.value?.wiki_urls || []
      if (wikiUrls.length === 1) {
        // 如果只有一个Wiki URL，直接打开
        window.open(wikiUrls[0], '_blank')
      } else if (wikiUrls.length > 1) {
        // 如果有多个Wiki URL，询问用户要打开哪个
        ElMessage.info(`发现 ${wikiUrls.length} 个Wiki页面，将依次打开`)
        wikiUrls.forEach((url, index) => {
          setTimeout(() => {
            window.open(url, '_blank')
          }, index * 500) // 每个链接间隔500ms打开，避免浏览器阻止弹窗
        })
      }
    }

    // 获取待审批的SQL工单数量
    const getPendingApprovalCount = () => {
      const sqlResults = analysisResult.value?.status_results?.sql_results || []
      return sqlResults.filter(item => item.status_code === 'PENDING_OPS_APPROVAL').length
    }

    // 检查是否可以批量审批
    const canBatchApprove = computed(() => {
      return getPendingApprovalCount() > 0
    })

    // 批量审批SQL工单
    const batchApproveSqlWorkflows = async () => {
      const sqlResults = analysisResult.value?.status_results?.sql_results || []
      const pendingApprovalWorkflows = sqlResults.filter(item =>
        item.status_code === 'PENDING_OPS_APPROVAL'
      )

      if (pendingApprovalWorkflows.length === 0) {
        ElMessage.warning('没有待审批的SQL工单')
        return
      }

      // 提取SQL工单URL
      const sqlUrls = pendingApprovalWorkflows.map(item => item.url).filter(url => url)

      if (sqlUrls.length === 0) {
        ElMessage.warning('没有找到有效的SQL工单URL')
        return
      }

      batchApprovalLoading.value = true
      try {
        const response = await batchApproveSql({
          sql_urls: sqlUrls,
          debug: false
        })

        if (response.data.success) {
          const { summary, details } = response.data.data

          // 显示审批结果
          ElMessage.success(
            `批量审批完成！成功: ${summary.success_count}, 失败: ${summary.failed_count}, 跳过: ${summary.skipped_count}`
          )

          // 显示详细结果
          if (details && details.length > 0) {
            const successCount = details.filter(d => d.success && d.action === 'approved').length
            const failedDetails = details.filter(d => !d.success || d.action === 'failed')

            if (successCount > 0) {
              ElMessage.success(`成功审批 ${successCount} 个SQL工单`)
            }

            if (failedDetails.length > 0) {
              failedDetails.forEach(detail => {
                if (detail.error) {
                  ElMessage.warning(`工单 ${detail.workflow_id}: ${detail.error}`)
                }
              })
            }
          }

          // 重新分析当前页面以更新状态
          if (wikiUrl.value) {
            setTimeout(() => {
              analyzeWiki()
            }, 1000)
          }
        } else {
          ElMessage.error(response.data.message || '批量审批失败')
        }
      } catch (error) {
        ElMessage.error('批量审批失败: ' + (error.response?.data?.message || error.message))
      } finally {
        batchApprovalLoading.value = false
      }
    }

    return {
      loading,
      batchApprovalLoading,
      wikiUrl,
      analysisResult,
      extractResult,
      analyzeWiki,
      extractLinks,
      checkExtractedLinks,
      getStatusType,
      getOverallStatusType,
      getOverallStatusText,
      getStatusIcon,
      getGitStatusSummary,
      getSqlStatusSummary,
      getApolloStatusSummary,
      getGitCardClass,
      getSqlCardClass,
      getApolloCardClass,
      openLink,
      openWikiUrls,
      getPendingApprovalCount,
      canBatchApprove,
      batchApproveSqlWorkflows
    }
  }
}
</script>

<style scoped>
.wiki-status-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.operation-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.wiki-button {
  border-radius: 20px;
  font-weight: 500;
}

.batch-approve-button {
  border-radius: 20px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.operation-row {
  margin-bottom: 0;
}

.result-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.summary-section {
  margin-bottom: 32px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.summary-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.overall-status-tag {
  font-size: 14px;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 20px;
}

.summary-cards {
  padding: 20px;
}

.summary-card {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 12px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transition: all 0.3s ease;
}

.apollo-card {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

.git-card {
  background: linear-gradient(135deg, #f3e5f5 0%, #ce93d8 100%);
}

.sql-card {
  background: linear-gradient(135deg, #e8f5e8 0%, #a5d6a7 100%);
}

.total-card {
  background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
}

.card-icon {
  margin-right: 16px;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #409eff;
}

.card-content {
  flex: 1;
}

.git-status-card .card-content,
.sql-status-card .card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.card-title {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.result-section {
  margin-bottom: 32px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.section-header .header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.section-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-summary {
  display: flex;
  align-items: center;
}

.summary-text {
  font-size: 14px;
  color: #606266;
  background: #f5f7fa;
  padding: 6px 12px;
  border-radius: 16px;
}

.apollo-cards-container,
.git-cards-container,
.sql-cards-container {
  padding: 20px;
}

.apollo-card-col,
.git-card-col,
.sql-card-col {
  margin-bottom: 16px;
}

.apollo-status-card,
.git-status-card,
.sql-status-card {
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  background: white;
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

.apollo-status-card:hover,
.git-status-card:hover,
.sql-status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.status-success {
  border-color: #67c23a;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6fffa 100%);
}

.status-warning {
  border-color: #e6a23c;
  background: linear-gradient(135deg, #fffbf0 0%, #fef3e2 100%);
}

.status-danger {
  border-color: #f56c6c;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.status-indicator {
  display: flex;
  align-items: center;
}

.status-tag {
  font-size: 14px;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-icon {
  font-size: 16px;
}

.view-button {
  border-radius: 20px;
  padding: 8px 16px;
  font-weight: 500;
}

.merge-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #303133;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.merge-info,
.sql-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  font-weight: 600;
  color: #606266;
  min-width: 60px;
  font-size: 12px;
}

.value {
  color: #303133;
  font-size: 12px;
}

.branch-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.branch-info code {
  background: #f5f7fa;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #409eff;
  font-weight: 500;
}

.apollo-info,
.workflow-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.apollo-id,
.workflow-id {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

.apollo-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.suggestion-row .value {
  color: #e6a23c;
  font-weight: 500;
}

.link-tag {
  display: block;
  margin-bottom: 8px;
  word-break: break-all;
}

.link-tag .el-button {
  padding: 0;
  height: auto;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .summary-cards {
    padding: 16px;
  }

  .summary-card {
    padding: 16px;
  }

  .card-value {
    font-size: 24px;
  }

  .apollo-cards-container,
  .git-cards-container,
  .sql-cards-container {
    padding: 16px;
  }

  .apollo-status-card,
  .git-status-card,
  .sql-status-card {
    padding: 16px;
    min-height: 180px;
  }

  .card-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
    margin-bottom: 12px;
  }

  .status-indicator {
    justify-content: center;
  }

  .merge-title {
    font-size: 13px;
    -webkit-line-clamp: 3;
  }

  .apollo-info,
  .workflow-info {
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .apollo-status-card,
  .git-status-card,
  .sql-status-card {
    min-height: 160px;
  }

  .status-tag {
    font-size: 12px;
    padding: 6px 12px;
  }

  .view-button {
    padding: 6px 12px;
    font-size: 12px;
  }
}
</style>
