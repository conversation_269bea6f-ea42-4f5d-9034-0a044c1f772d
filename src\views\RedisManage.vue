<template>
  <div class="redis-manage-container">
    <el-tabs v-model="activeTab">
      <!-- 集群管理标签页 -->
      <el-tab-pane label="集群管理" name="clusters">
        <div class="action-bar">
          <el-button type="primary" @click="showAddClusterDialog">添加集群</el-button>
        </div>
        
        <el-table :data="clusterList" border style="width: 100%">
          <el-table-column prop="cluster_name" label="集群名称" />
          <el-table-column prop="host" label="主机地址" />
          <el-table-column prop="port" label="端口" width="100" />
          <el-table-column prop="version" label="版本" width="100" />
          <el-table-column label="is_cluster" width="100">
          <template #default="scope">
              <el-tag :type="scope.row.is_cluster ? 'success' : 'danger'">
                {{ scope.row.is_cluster ? '集群' : '单点' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="status" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status ? 'success' : 'danger'">
                {{ scope.row.status ? '在线' : '离线' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250">
            <template #default="scope">
              <el-button size="small" @click="viewKeys(scope.row)">键管理</el-button>
              <el-button size="small" type="primary" @click="editCluster(scope.row)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteCluster(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      
      <!-- 键管理标签页 -->
      <el-tab-pane label="键管理" name="keys" v-if="selectedCluster">
        <div class="cluster-info">
          <h3>集群: {{ selectedCluster.name }} ({{ selectedCluster.host }}:{{ selectedCluster.port }})</h3>
          <div class="key-search">
            <el-input 
              v-model="keyPattern" 
              placeholder="输入键名模式进行搜索" 
              clearable 
              style="width: 300px;"
            >
              <template #append>
                <el-button @click="searchKeys">搜索</el-button>
              </template>
            </el-input>
            <el-button type="primary" @click="showAddKeyDialog">添加键</el-button>
          </div>
        </div>
        
        <el-table :data="keysList" border style="width: 100%" v-loading="keysLoading">
          <el-table-column prop="key" label="键名" show-overflow-tooltip />
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button size="small" @click="viewKeyValue(scope.row.key)">查看值</el-button>
              <el-button size="small" type="primary" @click="editKey(scope.row.key)">编辑</el-button>
              <el-button size="small" type="danger" @click="confirmDeleteKey(scope.row.key)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-area" v-if="keysList.length > 0">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
    
    <!-- 添加/编辑集群对话框 -->
    <el-dialog 
      :title="dialogType === 'add' ? '添加集群' : '编辑集群'" 
      v-model="clusterDialogVisible"
      width="500px"
    >
      <el-form :model="clusterForm" label-width="100px" :rules="clusterRules" ref="clusterFormRef">
        <el-form-item label="集群名称" prop="cluster_name">
          <el-input v-model="clusterForm.cluster_name" placeholder="请输入集群名称"></el-input>
        </el-form-item>
        <el-form-item label="主机地址" prop="host">
          <el-input v-model="clusterForm.host" placeholder="请输入主机地址"></el-input>
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input-number v-model="clusterForm.port" :min="1" :max="65535" placeholder="请输入端口"></el-input-number>
        </el-form-item>
        <el-form-item label="版本" prop="version">
          <el-input v-model="clusterForm.version" placeholder="请输入版本号"></el-input>
        </el-form-item>
        <el-form-item label="集群" prop="is_cluster">
          <el-switch v-model="clusterForm.is_cluster" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="clusterForm.password" type="password" placeholder="请输入密码（可选）"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch v-model="clusterForm.status" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="clusterDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitClusterForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 添加/编辑键对话框 -->
    <el-dialog 
      :title="keyDialogType === 'add' ? '添加键' : '编辑键'" 
      v-model="keyDialogVisible"
      width="600px"
    >
      <el-form :model="keyForm" label-width="100px" :rules="keyRules" ref="keyFormRef">
        <el-form-item label="键名" prop="key">
          <el-input v-model="keyForm.key" placeholder="请输入键名" :disabled="keyDialogType === 'edit'"></el-input>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="keyForm.type" placeholder="请选择类型" :disabled="keyDialogType === 'edit'">
            <el-option label="字符串" value="string"></el-option>
            <el-option label="列表" value="list"></el-option>
            <el-option label="集合" value="set"></el-option>
            <el-option label="有序集合" value="zset"></el-option>
            <el-option label="哈希表" value="hash"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="值" prop="value">
          <el-input 
            v-model="keyForm.value" 
            type="textarea" 
            :rows="5" 
            placeholder="请输入值（JSON格式）"
          ></el-input>
        </el-form-item>
        <el-form-item label="过期时间" prop="ttl">
          <el-input-number v-model="keyForm.ttl" :min="-1" placeholder="-1表示永不过期"></el-input-number>
          <span class="ttl-unit">秒</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="keyDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitKeyForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 查看键值对话框 -->
    <el-dialog title="键值详情" v-model="keyValueDialogVisible">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="键名">{{ currentKey }}</el-descriptions-item>
        <el-descriptions-item label="类型">{{ keyValueInfo.type }}</el-descriptions-item>
        <el-descriptions-item label="TTL">{{ keyValueInfo.ttl }}</el-descriptions-item>

        <el-descriptions-item label="值">
          <pre v-if="keyValueInfo.value !== undefined">{{ keyValueInfo.value }}</pre>
          <pre v-else>值不存在或为undefined</pre>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'

export default {
  name: 'RedisManage',
  setup() {
    // 状态变量
    const activeTab = ref('clusters') // 默认显示集群管理标签页
    const clusterList = ref([])
    const selectedCluster = ref(null)
    const clusterInfo = ref({})
    const keysList = ref([])
    const keysLoading = ref(false)
    const keyPattern = ref('')
    const currentPage = ref(1)
    const pageSize = ref(10)
    const total = ref(0)
    const currentKey = ref('')
    const keyValueInfo = ref({})
    const keyValueLoading = ref(false)
    
    // 对话框控制
    const clusterDialogVisible = ref(false)
    const keyDialogVisible = ref(false)
    const keyValueDialogVisible = ref(false)
    const dialogType = ref('add') // 'add' 或 'edit'
    const keyDialogType = ref('add') // 'add' 或 'edit'
    
    // 表单引用
    const clusterFormRef = ref(null)
    const keyFormRef = ref(null)
    
    // 集群表单数据
    const clusterForm = reactive({      
      id: '',
      cluster_name: '',
      host: '',
      port: 6379,
      version: '',
      password: '',
      status: true,
      is_cluster: false
    })

    // 键表单数据
    const keyForm = reactive({
      key: '',
      type: 'string',
      value: '',
      ttl: -1
    })
    
    // 表单验证规则
    const clusterRules = {
      cluster_name: [{ required: true, message: '请输入集群名称', trigger: 'blur' }],
      host: [{ required: true, message: '请输入主机地址', trigger: 'blur' }],
      port: [{ required: true, message: '请输入端口', trigger: 'blur' }],
      db: [{ required: true, message: '请输入数据库编号', trigger: 'blur' }]
    }
    
    const keyRules = {
      key: [{ required: true, message: '请输入键名', trigger: 'blur' }],
      type: [{ required: true, message: '请选择类型', trigger: 'change' }],
      value: [{ required: true, message: '请输入值', trigger: 'blur' }]
    }
    
    // 初始化
    onMounted(() => {
      fetchClusters()
    })
    
    // 获取集群列表
    const fetchClusters = async () => {
      try {
        const response = await axios.get('/api/redis/clusters', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        })
        if (response.data.success) {
          clusterList.value = response.data.data || []
        } else {
          ElMessage.error(response.data.message || '获取集群列表失败')
        }
      } catch (error) {
        console.error('获取集群列表失败:', error)
        ElMessage.error('获取集群列表失败')
      }
    }
    
    // 刷新集群信息
    const refreshClusterInfo = async () => {
      if (!selectedCluster.value) return
      
      try {
        const response = await axios.get(`/api/redis/clusters/${selectedCluster.value.id}/info`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        })
        if (response.data.success) {
          clusterInfo.value = response.data.data || {}
        } else {
          ElMessage.error(response.data.message || '获取集群信息失败')
        }
      } catch (error) {
        console.error('获取集群信息失败:', error)
        ElMessage.error('获取集群信息失败')
      }
    }
    
    // 查看键列表
    const viewKeys = async (cluster) => {
      selectedCluster.value = cluster
      activeTab.value = 'keys'
      keyPattern.value = ''
      await searchKeys()
    }
    
    const searchKeys = async () => {
      console.log('selectedCluster:', selectedCluster.value)
      if (!selectedCluster.value) return
      
      keysLoading.value = true
      try {
        const pattern = keyPattern.value || '*'
        // 发送请求
        const response = await axios.get(`/api/redis/clusters/${selectedCluster.value.id}/keys/${encodeURIComponent(pattern)}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        })
        
        if (response.data.success) {
          console.log('键列表原始数据:', response.data.data);
          
          // 处理返回的数据可能是对象的情况
          if (typeof response.data.data === 'object' && !Array.isArray(response.data.data)) {
            // 对象格式，将键名转换为数组
            const keys = Object.keys(response.data.data);
            keysList.value = keys.map(key => {
              return {
                key: key,
                type: response.data.data[key]?.type,
                ttl: response.data.data[key]?.ttl
              }
            });
          } else {
            // 数组格式处理
            const keys = Array.isArray(response.data.data) 
              ? response.data.data 
              : [];
            
            keysList.value = keys.map(item => {
              return {
                key: item?.key ?? item?.name ?? String(item)
              }
            });
          }
          total.value = keysList.value.length;
        } else {
          ElMessage.error(response.data.message || '获取键列表失败')
        }
      } catch (error) {
        console.error('获取键列表失败:', error)
        ElMessage.error('获取键列表失败')
      } finally {
        keysLoading.value = false
      }
    }
    
    // 查看键值
    const viewKeyValue = async (key) => {
      if (!selectedCluster.value) return
      
      currentKey.value = key
      keyValueDialogVisible.value = true
      keyValueLoading.value = true
      
      try {
        // 发送请求
        const response = await axios.get(`/api/redis/clusters/${selectedCluster.value.id}/keys/${encodeURIComponent(key)}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        })
        if (response.data.success) {
          console.log('键值详情原始数据:', response.data.data);
          
          // 处理返回的数据可能是对象的情况
          if (typeof response.data.data === 'object' && !Array.isArray(response.data.data)) {
            // 如果返回的是对象，并且包含了键名作为属性
            if (response.data.data[key]) {
              keyValueInfo.value = response.data.data[key];
            } else {
              // 直接使用返回的对象
              keyValueInfo.value = response.data.data;
            }
          } else {
            keyValueInfo.value = response.data.data || {};
          }
          
          // 调试输出处理后的键值信息
          console.log('处理后的键值信息:', keyValueInfo.value);
        } else {
          ElMessage.error(response.data.message || '获取键值失败')
        }
      } catch (error) {
        console.error('获取键值失败:', error)
        ElMessage.error('获取键值失败')
      } finally {
        keyValueLoading.value = false
      }
    }
    
    // 格式化值显示
    const formatValue = (value) => {
      if (typeof value === 'object') {
        return JSON.stringify(value, null, 2)
      }
      return value
    }
    
    // 格式化键值显示
    const formatKeyValue = (value) => {
      try {
        console.log('formatKeyValue接收到的值类型:', typeof value, '值:', value);
        
        // 如果值是undefined或null，返回空字符串
        if (value === undefined || value === null) {
          return '';
        }
        
        // 如果值是字符串，尝试解析JSON
        if (typeof value === 'string') {
          try {
            // 尝试解析为JSON对象
            const parsedValue = JSON.parse(value);
            return JSON.stringify(parsedValue, null, 2);
          } catch (e) {
            // 如果解析失败，直接返回原始值
            console.log('JSON解析失败，返回原始字符串');
            return value;
          }
        }
        // 如果值已经是对象，直接格式化
        else if (typeof value === 'object') {
          return JSON.stringify(value, null, 2);
        }
        // 其他类型直接返回字符串形式
        return String(value);
      } catch (error) {
        console.error('格式化键值失败:', error);
        return String(value);
      }
    }
    
    // 分页处理
    const handleSizeChange = (val) => {
      pageSize.value = val
      // 重新计算分页
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      // 这里假设已经获取了所有键，只是在前端进行分页
      // 实际项目中可能需要从后端重新获取数据
    }
    
    const handleCurrentChange = (val) => {
      currentPage.value = val
      // 同上，重新计算分页
    }
    
    // 添加集群
    const showAddClusterDialog = () => {
      dialogType.value = 'add'
      resetClusterForm()
      clusterDialogVisible.value = true
    }
    
    // 编辑集群
    const editCluster = (cluster) => {
      dialogType.value = 'edit'
      resetClusterForm()
      Object.keys(clusterForm).forEach(key => {
        if (key in cluster) {
          clusterForm[key] = cluster[key]
        }
      })
      clusterDialogVisible.value = true
    }
    
    // 删除集群
    const deleteCluster = (cluster) => {
      ElMessageBox.confirm(
        '确定要删除这个集群吗？',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        try {
          const response = await axios.delete(`/api/redis/clusters/${cluster.id}`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })
          if (response.data.success) {
            ElMessage.success('删除成功')
            fetchClusters()
            if (selectedCluster.value && selectedCluster.value.id === cluster.id) {
              selectedCluster.value = null
              activeTab.value = 'clusters'
            }
          } else {
            ElMessage.error(response.data.message || '删除失败')
          }
        } catch (error) {
          console.error('删除集群失败:', error)
          ElMessage.error('删除集群失败')
        }
      }).catch(() => {
        // 取消删除
      })
    }
    
    // 重置集群表单
    const resetClusterForm = () => {
      clusterForm.id = ''
      clusterForm.name = ''
      clusterForm.host = ''
      clusterForm.port = 6379
      clusterForm.version = ''
      clusterForm.password = ''
      clusterForm.status = true
      clusterForm.is_cluster = false
    }
    
    // 提交集群表单
    const submitClusterForm = async () => {
      if (!clusterFormRef.value) return
      
      await clusterFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            let response
            if (dialogType.value === 'add') {
              response = await axios.post('/api/redis/clusters', clusterForm, {
                headers: {
                  'Authorization': `Bearer ${localStorage.getItem('token')}`,
                  'Content-Type': 'application/json'
                }
              })
            } else {
              response = await axios.put(`/api/redis/clusters/${clusterForm.id}`, clusterForm, {
                headers: {
                  'Authorization': `Bearer ${localStorage.getItem('token')}`,
                  'Content-Type': 'application/json'
                }
              })
            }
            
            if (response.data.success) {
              ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
              clusterDialogVisible.value = false
              fetchClusters()
            } else {
              ElMessage.error(response.data.message || (dialogType.value === 'add' ? '添加失败' : '更新失败'))
            }
          } catch (error) {
            console.error(dialogType.value === 'add' ? '添加集群失败:' : '更新集群失败:', error)
            ElMessage.error(dialogType.value === 'add' ? '添加集群失败' : '更新集群失败')
          }
        }
      })
    }
    
    // 添加键
    const showAddKeyDialog = () => {
      keyDialogType.value = 'add'
      resetKeyForm()
      keyDialogVisible.value = true
    }
    
    // 编辑键
    const editKey = async (key) => {
      if (!selectedCluster.value) return
      
      keyDialogType.value = 'edit'
      resetKeyForm()
      keyForm.key = key
      
      try {
        const response = await axios.get(`/api/redis/clusters/${selectedCluster.value.id}/keys/${encodeURIComponent(key)}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        })
        if (response.data.success) {
          const data = response.data.data || {}
          keyForm.type = data.type || 'string'
          keyForm.value = typeof data.value === 'object' ? JSON.stringify(data.value, null, 2) : data.value
          keyForm.ttl = data.ttl || -1
          keyDialogVisible.value = true
        } else {
          ElMessage.error(response.data.message || '获取键值失败')
        }
      } catch (error) {
        console.error('获取键值失败:', error)
        ElMessage.error('获取键值失败')
      }
    }
    
    // 确认删除键
    const confirmDeleteKey = (key) => {
      ElMessageBox.confirm(
        '确定要删除这个键吗？',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        try {
          const response = await axios.delete(`/api/redis/clusters/${selectedCluster.value.id}/keys/${encodeURIComponent(key)}`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })
          if (response.data.success) {
            ElMessage.success('删除成功')
            searchKeys()
          } else {
            ElMessage.error(response.data.message || '删除失败')
          }
        } catch (error) {
          console.error('删除键失败:', error)
          ElMessage.error('删除键失败')
        }
      }).catch(() => {
        // 取消删除
      })
    }
    
    // 重置键表单
    const resetKeyForm = () => {
      keyForm.key = ''
      keyForm.type = 'string'
      keyForm.value = ''
      keyForm.ttl = -1
    }
    
    // 提交键表单
    const submitKeyForm = async () => {
      if (!keyFormRef.value) return
      
      await keyFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            // 尝试解析值（如果是JSON格式）
            let parsedValue = keyForm.value
            try {
              if (keyForm.type !== 'string') {
                parsedValue = JSON.parse(keyForm.value)
              }
            } catch (e) {
              ElMessage.warning('值不是有效的JSON格式，将作为字符串处理')
            }
            
            const data = {
              key: keyForm.key,
              type: keyForm.type,
              value: parsedValue,
              ttl: keyForm.ttl
            }
            
            const response = await axios.post(`/api/redis/clusters/${selectedCluster.value.id}/keys`, data, {
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json'
              }
            })
            if (response.data.success) {
              ElMessage.success(keyDialogType.value === 'add' ? '添加成功' : '更新成功')
              keyDialogVisible.value = false
              searchKeys()
            } else {
              ElMessage.error(response.data.message || (keyDialogType.value === 'add' ? '添加失败' : '更新失败'))
            }
          } catch (error) {
            console.error(keyDialogType.value === 'add' ? '添加键失败:' : '更新键失败:', error)
            ElMessage.error(keyDialogType.value === 'add' ? '添加键失败' : '更新键失败')
          }
        }
      })
    }
    
    return {
      activeTab,
      clusterList,
      selectedCluster,
      clusterInfo,
      keysList,
      keysLoading,
      keyPattern,
      currentPage,
      pageSize,
      total,
      currentKey,
      keyValueInfo,
      keyValueLoading,
      clusterDialogVisible,
      keyDialogVisible,
      keyValueDialogVisible,
      dialogType,
      keyDialogType,
      clusterFormRef,
      keyFormRef,
      clusterForm,
      keyForm,
      clusterRules,
      keyRules,
      refreshClusterInfo,
      viewKeys,
      searchKeys,
      viewKeyValue,
      formatValue,
      handleSizeChange,
      handleCurrentChange,
      showAddClusterDialog,
      editCluster,
      deleteCluster,
      submitClusterForm,
      showAddKeyDialog,
      editKey,
      confirmDeleteKey,
      submitKeyForm
    }
  }
}
</script>

<style scoped>
.redis-manage-container {
  padding: 20px;
}

.action-bar {
  margin-bottom: 20px;
}

.cluster-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.key-search {
  display: flex;
  gap: 10px;
}

.pagination-area {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.loading-container {
  padding: 20px;
}

.key-value-content {
  margin-top: 20px;
}

.key-value-content pre {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
  max-height: 300px;
}

.ttl-unit {
  margin-left: 10px;
}
</style>