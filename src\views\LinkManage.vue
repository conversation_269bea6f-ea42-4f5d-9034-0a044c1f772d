<template>
  <div class="link-manage-container">
    <div class="search-area">
      <el-form :inline="true">
        <el-form-item label="分类">
          <el-select v-model="searchForm.category" placeholder="选择分类" clearable style="min-width:200px">
            <el-option 
              v-for="category in categoryOptions" 
              :key="category" 
              :label="category" 
              :value="category"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="环境">
          <el-select v-model="searchForm.environment" placeholder="选择环境" clearable style="min-width:200px">
            <el-option 
              v-for="env in environmentOptions" 
              :key="env" 
              :label="env" 
              :value="env"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="fetchLinks">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="operation-area">
      <el-button type="primary" @click="handleAdd">添加链接</el-button>
    </div>

    <el-table :data="tableData" border style="width: 100%" v-loading="loading">
      <el-table-column prop="title" label="标题" min-width="120" />
      <el-table-column prop="url" label="链接地址" min-width="200">
        <template #default="{row}">
          <el-link type="primary" :href="row.url" target="_blank">{{ row.url }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="category" label="分类" min-width="100" />
      <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
      <el-table-column prop="icon" label="图标" min-width="80">
        <template #default="{row}">
          <template v-if="isCustomIcon(row.icon)">
            <img :src="row.icon" style="max-width: 24px; max-height: 24px;" />
          </template>
          <template v-else>
            <i :class="`fa ${row.icon}`"></i>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="environment" label="环境" min-width="80">
        <template #default="{row}">
          <el-tag 
            :type="row.environment === 'prod' ? 'success' : row.environment === 'test' ? 'warning' : 'info'"
          >
            {{ row.environment }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{row}">
          <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
          <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-area">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑链接对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加链接' : '编辑链接'"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入链接标题" />
        </el-form-item>
        <el-form-item label="链接地址" prop="url">
          <el-input v-model="form.url" placeholder="请输入链接地址" />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select 
            v-model="form.category" 
            placeholder="请选择分类"
            filterable
            allow-create
            default-first-option
          >
            <el-option 
              v-for="category in categoryOptions" 
              :key="category" 
              :label="category" 
              :value="category"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            placeholder="请输入链接描述"
          />
        </el-form-item>
        <el-form-item label="图标" prop="icon">
        <div class="icon-selector">
          <!-- 已有图标选择 -->
          <el-select 
            v-model="form.icon" 
            filterable 
            clearable
            placeholder="选择已有图标"
            style="width: 100%; margin-bottom: 10px;"
          >
            <el-option
              v-for="icon in iconOptions"
              :key="icon.value"
              :label="icon.label"
              :value="icon.value"
            >
              <div style="display: flex; align-items: center;">
                <i :class="`fa ${icon.value}`" style="margin-right: 8px;"></i>
                <span>{{ icon.label }}</span>
              </div>
            </el-option>
          </el-select>
          
          <!-- 或上传新图标 -->
          <div style="margin-top: 10px;">
            <el-upload
              action="api/upload/icon"
              :headers="uploadHeaders"
              :data="{title: form.title, filename: form.title}"
              :show-file-list="false"
              :on-success="handleIconUploadSuccess"
              :on-error="handleIconUploadError"
              :before-upload="beforeIconUpload"
            >
              <el-button type="primary" size="small">上传新图标</el-button>
              <template #tip>
                <div class="el-upload__tip">支持 ico、svg、png 等格式，文件名将以标题命名</div>
              </template>
            </el-upload>
          </div>
          
          <!-- 预览当前图标 -->
          <div v-if="form.icon" style="margin-top: 10px; display: flex; align-items: center;">
            <span style="margin-right: 10px;">当前图标:</span>
            <template v-if="isCustomIcon(form.icon)">
              <img :src="form.icon" style="max-width: 24px; max-height: 24px;" />
            </template>
            <template v-else>
              <i :class="`fa ${form.icon}`" style="font-size: 24px;"></i>
            </template>
          </div>
        </div>
      </el-form-item>
        <el-form-item label="环境" prop="environment">
          <el-select v-model="form.environment" placeholder="请选择环境">
            <el-option 
              v-for="env in environmentOptions" 
              :key="env" 
              :label="env" 
              :value="env"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '../api'

export default {
  name: 'LinkManage',
  setup() {
    // 表格数据
    const tableData = ref([])
    const loading = ref(false)
    const currentPage = ref(1)
    const pageSize = ref(10)
    const total = ref(0)

    // 搜索表单
    const searchForm = reactive({
      category: '',
      environment: ''
    })

    // 分类选项，从已有数据中提取
    const categoryOptions = ref([])
    
    // 环境选项，静态定义
    const environmentOptions = ref([])

    // 对话框相关
    const dialogVisible = ref(false)
    const dialogType = ref('add') // 'add' 或 'edit'
    const formRef = ref(null)
    const submitLoading = ref(false)

    // 表单数据
    const form = reactive({
      id: '',
      title: '',
      url: '',
      category: '',
      description: '',
      icon: 'fa-link',
      environment: ''
    })

    // 表单验证规则
    const rules = {
      title: [
        { required: true, message: '请输入链接标题', trigger: 'blur' }
      ],
      url: [
        { required: true, message: '请输入链接地址', trigger: 'blur' },
        { pattern: /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/, message: '请输入有效的URL地址', trigger: 'blur' }
      ],
      category: [
        { required: true, message: '请选择或输入分类', trigger: 'change' }
      ],
      environment: [
        { required: true, message: '请选择环境', trigger: 'change' }
      ]
    }



    // 获取链接列表
    const fetchLinks = async () => {
      loading.value = true
      try {
        const params = {}
        if (searchForm.category) params.category = searchForm.category
        if (searchForm.environment) params.environment = searchForm.environment

        const response = await api.get('/links', { params })

        tableData.value = response.data.data || []
        total.value = response.data.total || tableData.value.length

        // 提取所有分类
        const categories = new Set(tableData.value.map(item => item.category).filter(Boolean))
        categoryOptions.value = Array.from(categories)
        const environments = new Set(tableData.value.map(item => item.environment).filter(Boolean))
        environmentOptions.value = Array.from(environments)
      } catch (error) {
        console.error('获取链接列表失败:', error)
        ElMessage.error('获取链接列表失败')
      } finally {
        loading.value = false
      }
    }

    // 重置搜索条件
    const resetSearch = () => {
      searchForm.category = ''
      searchForm.environment = ''
      fetchLinks()
    }

    // 分页处理
    const handleSizeChange = (val) => {
      pageSize.value = val
      fetchLinks()
    }

    const handleCurrentChange = (val) => {
      currentPage.value = val
      fetchLinks()
    }

    // 添加链接
    const handleAdd = () => {
      dialogType.value = 'add'
      resetForm()
      dialogVisible.value = true
    }

    // 编辑链接
    const handleEdit = (row) => {
      dialogType.value = 'edit'
      resetForm()
      Object.keys(form).forEach(key => {
        if (key in row) {
          form[key] = row[key]
        }
      })
      dialogVisible.value = true
    }

    // 删除链接
    const handleDelete = (row) => {
      ElMessageBox.confirm(
        '确定要删除这个链接吗？',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        try {
          await api.delete(`/links/${row.id}`)
          ElMessage.success('删除成功')
          fetchLinks()
        } catch (error) {
          console.error('删除链接失败:', error)
          ElMessage.error('删除链接失败')
        }
      }).catch(() => {
        // 取消删除
      })
    }

    // 重置表单
    const resetForm = () => {
      form.id = ''
      form.title = ''
      form.url = ''
      form.category = ''
      form.description = ''
      form.icon = 'fa-link'
      form.environment = ''
    }

    // 提交表单
    const submitForm = async () => {
      if (!formRef.value) return

      await formRef.value.validate(async (valid) => {
        if (valid) {
          submitLoading.value = true
          try {
            if (dialogType.value === 'add') {
              // 创建新链接
              await api.post('/links', form)
              ElMessage.success('添加成功')
            } else {
              // 更新链接
              await api.put(`/links/${form.id}`, form)
              ElMessage.success('更新成功')
            }
            dialogVisible.value = false
            fetchLinks()
          } catch (error) {
            console.error(dialogType.value === 'add' ? '添加链接失败:' : '更新链接失败:', error)
            ElMessage.error(dialogType.value === 'add' ? '添加链接失败' : '更新链接失败')
          } finally {
            submitLoading.value = false
          }
        }
      })
    }

    onMounted(() => {
      fetchLinks()
      fetchIcons()
    })

    // 图标选项
    const iconOptions = ref([])
    
    // 获取图标列表
    const fetchIcons = async () => {
      try {
        const response = await api.get('/links/icons')
        if (response.data.success && response.data.code === 200) {
          // 将API返回的图标数据转换为组件需要的格式
          iconOptions.value = response.data.data.map(icon => ({
            label: icon.name,
            value: icon.url
          }))
        } else {
          ElMessage.error(response.data.message || '获取图标列表失败')
        }
      } catch (error) {
        console.error('获取图标列表失败:', error)
        ElMessage.error('获取图标列表失败')
      }
    }

    // 上传头信息
    const uploadHeaders = computed(() => {
      return {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })

    // 判断是否为自定义图标（上传的图片）
    const isCustomIcon = (icon) => {
      return icon && (icon.startsWith('http') || icon.startsWith('/uploads') || icon.startsWith('/static/uploads'))
    }

    // 图标上传前的验证
    const beforeIconUpload = (file) => {
      const isValidType = ['image/svg+xml', 'image/png', 'image/jpeg', 'image/x-icon'].includes(file.type)
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isValidType) {
        ElMessage.error('图标只能是 SVG/PNG/JPG/ICO 格式!')
        return false
      }
      if (!isLt2M) {
        ElMessage.error('图标大小不能超过 2MB!')
        return false
      }
      return true
    }

    // 图标上传成功处理
    const handleIconUploadSuccess = (response) => {
      if (response.success) {
        form.icon = response.data.url
        // 刷新图标列表
        fetchIcons()
        ElMessage.success('图标上传成功')
      } else {
        ElMessage.error(response.message || '图标上传失败')
      }
    }

    // 图标上传失败处理
    const handleIconUploadError = () => {
      ElMessage.error('图标上传失败，请重试')
    }

    return {
      tableData,
      loading,
      currentPage,
      pageSize,
      total,
      searchForm,
      categoryOptions,
      environmentOptions,
      dialogVisible,
      dialogType,
      formRef,
      form,
      rules,
      submitLoading,
      fetchLinks,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      handleAdd,
      handleEdit,
      handleDelete,
      submitForm,
      // 图标相关
      iconOptions,
      uploadHeaders,
      isCustomIcon,
      beforeIconUpload,
      handleIconUploadSuccess,
      handleIconUploadError
    }
  }
}
</script>

<style scoped>
.link-manage-container {
  padding: 20px;
}

.search-area {
  margin-bottom: 20px;
}

.operation-area {
  margin-bottom: 20px;
}

.pagination-area {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>