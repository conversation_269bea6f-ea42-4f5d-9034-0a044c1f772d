<template>
  <div class="nginx-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="nginx-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">域名访问控制</span>
              <el-button
                type="primary"
                @click="refreshStatus"
                :loading="loading"
                class="refresh-btn"
              >
                <el-icon><Refresh /></el-icon>
                <span class="btn-text">刷新</span>
              </el-button>
            </div>
          </template>

          <!-- 移动端卡片视图 -->
          <div class="mobile-view" v-if="isMobile">
            <div
              v-for="domain in domainList"
              :key="domain.domain"
              class="domain-card"
            >
              <div class="domain-info">
                <div class="domain-name">{{ domain.domain }}</div>
                <div class="domain-status">
                  <el-tag
                    :type="domain.status === 'allow' ? 'success' : 'danger'"
                    class="status-tag"
                  >
                    {{ domain.status === 'allow' ? '允许访问' : '拒绝访问' }}
                  </el-tag>
                </div>
              </div>
              <div class="domain-actions">
                <el-button
                  type="success"
                  size="small"
                  :disabled="domain.status === 'allow'"
                  @click="changeAccess(domain.domain, 'allow')"
                  class="action-btn allow-btn"
                >
                  <el-icon><Check /></el-icon>
                  允许
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  :disabled="domain.status === 'deny'"
                  @click="changeAccess(domain.domain, 'deny')"
                  class="action-btn deny-btn"
                >
                  <el-icon><Close /></el-icon>
                  拒绝
                </el-button>
              </div>
            </div>
          </div>

          <!-- 桌面端表格视图 -->
          <div class="desktop-view" v-else>
            <el-table
              :data="domainList"
              v-loading="loading"
              style="width: 100%"
              class="domain-table"
            >
              <el-table-column prop="domain" label="域名" min-width="200">
                <template #default="scope">
                  <div class="domain-cell">{{ scope.row.domain }}</div>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="外网访问状态" width="150" align="center">
                <template #default="scope">
                  <el-tag :type="scope.row.status === 'allow' ? 'success' : 'danger'">
                    {{ scope.row.status === 'allow' ? '允许' : '拒绝' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" min-width="280">
                <template #default="scope">
                  <div class="table-actions">
                    <el-button
                      type="success"
                      size="small"
                      :disabled="scope.row.status === 'allow'"
                      @click="changeAccess(scope.row.domain, 'allow')"
                      class="table-action-btn"
                    >
                      <el-icon><Check /></el-icon>
                      允许外网访问
                    </el-button>
                    <el-button
                      type="danger"
                      size="small"
                      :disabled="scope.row.status === 'deny'"
                      @click="changeAccess(scope.row.domain, 'deny')"
                      class="table-action-btn"
                    >
                      <el-icon><Close /></el-icon>
                      拒绝外网访问
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Check, Close } from '@element-plus/icons-vue'

export default {
  name: 'NginxControl',
  components: {
    Refresh,
    Check,
    Close
  },
  setup() {
    const domainList = ref([])
    const loading = ref(false)
    const isMobile = ref(false)

    // 检测是否为移动设备
    const checkMobile = () => {
      isMobile.value = window.innerWidth <= 768
    }

    // 监听窗口大小变化
    const handleResize = () => {
      checkMobile()
    }
    const refreshStatus = async () => {
      loading.value = true
      try {
        const response = await fetch('/api/nginx/access-control', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({ action: 'status' })
        })
        
        const data = await response.json()
        if (data.success) {
          domainList.value = (data.domains || []).map(item => ({
            domain: item.name,
            status: item.status
          }))
          ElMessage.success('状态刷新成功')
        } else {
          ElMessage.error(data.message || '获取状态失败')
        }
      } catch (error) {
        ElMessage.error('获取状态失败，请检查网络连接')
        console.error(error)
      } finally {
        loading.value = false
      }
    }

    // 更改域名的访问状态
    const changeAccess = async (domain, action) => {
      loading.value = true
      try {
        const response = await fetch('/api/nginx/access-control', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({ action, domain })
        })
        
        const data = await response.json()
        if (data.success) {
          // 更新本地状态
          const index = domainList.value.findIndex(item => item.domain === domain)
          if (index !== -1) {
            domainList.value[index].status = action
          }
          
          ElMessage.success(action === 'allow' ? 
            `已允许外网访问 ${domain}` : 
            `已拒绝外网访问 ${domain}`
          )
        } else {
          ElMessage.error(data.message || '操作失败')
        }
      } catch (error) {
        ElMessage.error('操作失败，请检查网络连接')
        console.error(error)
      } finally {
        loading.value = false
      }
    }

    onMounted(() => {
      checkMobile()
      window.addEventListener('resize', handleResize)
      refreshStatus()
    })

    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
    })

    return {
      domainList,
      loading,
      isMobile,
      refreshStatus,
      changeAccess
    }
  }
}
</script>

<style scoped>
.nginx-container {
  padding: 20px;
  min-height: 100vh;
}

/* 响应式容器 */
@media (max-width: 768px) {
  .nginx-container {
    padding: 10px;
  }
}

.nginx-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}



@media (max-width: 480px) {
  .card-header {
    flex-direction: column;
    align-items: stretch;
  }

  .refresh-btn {
    width: 100%;
    justify-content: center;
  }

  .btn-text {
    margin-left: 4px;
  }
}

/* 移动端卡片视图样式 */
.mobile-view {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 10px 0;
}

.domain-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
}

.domain-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.domain-info {
  margin-bottom: 12px;
}

.domain-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  word-break: break-all;
}

.domain-status {
  display: flex;
  align-items: center;
}

.status-tag {
  font-weight: 500;
  padding: 4px 12px;
  border-radius: 20px;
}

.domain-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.action-btn {
  flex: 1;
  max-width: 120px;
  border-radius: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.allow-btn:not(:disabled) {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  border-color: #67c23a;
}

.deny-btn:not(:disabled) {
  background: linear-gradient(135deg, #f56c6c, #f78989);
  border-color: #f56c6c;
}

/* 桌面端表格视图样式 */
.desktop-view {
  margin-top: 20px;
}

.domain-table {
  border-radius: 8px;
  overflow: hidden;
}

.domain-cell {
  font-weight: 500;
  color: #303133;
  word-break: break-all;
}

.table-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.table-action-btn {
  border-radius: 6px;
  font-weight: 500;
  min-width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

/* 响应式表格 */
@media (max-width: 1024px) {
  .table-actions {
    flex-direction: column;
    gap: 4px;
  }

  .table-action-btn {
    min-width: 100px;
    font-size: 12px;
    padding: 4px 8px;
  }
}

/* 加载状态优化 */
:deep(.el-loading-mask) {
  border-radius: 12px;
}

/* 标签样式优化 */
:deep(.el-tag) {
  border-radius: 20px;
  font-weight: 500;
  padding: 4px 12px;
}

/* 按钮禁用状态优化 */
:deep(.el-button:disabled) {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f8f9fa;
}

/* 卡片头部样式优化 */
:deep(.el-card__header) {
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-bottom: 1px solid #e9ecef;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>