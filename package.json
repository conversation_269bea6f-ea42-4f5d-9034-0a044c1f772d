{"name": "server-management-system", "version": "1.0.0", "description": "服务器管理系统前端项目", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint --ext .js,.vue src/", "lint:fix": "eslint --ext .js,.vue src/ --fix", "clean": "rm -rf dist"}, "dependencies": {"vue": "^3.3.0", "vue-router": "^4.2.0", "vuex": "^4.1.0", "axios": "^1.6.0", "element-plus": "^2.4.0", "@element-plus/icons-vue": "^2.1.0", "echarts": "^5.4.2"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.0", "@vue/eslint-config-standard": "^8.0.1", "sass": "^1.69.0"}}