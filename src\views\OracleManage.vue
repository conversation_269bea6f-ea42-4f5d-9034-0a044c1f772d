<template>
  <div class="oracle-manage">
    <el-tabs v-model="activeTab" class="oracle-tabs">

      <el-tab-pane label="服务器管理" name="servers">
        <div class="action-bar">
          <el-button type="primary" @click="showAddServerDialog" class="add-server-btn">
            <el-icon><Plus /></el-icon>
            <span class="btn-text">添加服务器</span>
          </el-button>
        </div>

        <!-- 移动端卡片视图 -->
        <div class="mobile-view" v-if="isMobile">
          <div
            v-for="server in serverList"
            :key="server.id"
            class="server-card"
          >
            <div class="server-header">
              <div class="server-name">{{ server.server_name }}</div>
              <el-tag :type="server.status ? 'success' : 'danger'" class="status-tag">
                {{ server.status ? '启用' : '禁用' }}
              </el-tag>
            </div>

            <div class="server-info-mobile">
              <div class="info-item">
                <el-icon class="info-icon"><Monitor /></el-icon>
                <span class="info-label">服务器:</span>
                <span class="info-value">{{ server.ssh_address }}</span>
              </div>
            </div>

            <div class="server-actions">
              <el-button
                size="small"
                type="primary"
                @click="viewDiskSpace(server)"
                class="action-btn disk-btn"
              >
                <el-icon><FolderOpened /></el-icon>
                磁盘空间
              </el-button>
              <el-button
                size="small"
                type="success"
                @click="viewTablespace(server)"
                class="action-btn tablespace-btn"
              >
                <el-icon><DataBoard /></el-icon>
                表空间
              </el-button>
              <el-button
                size="small"
                type="info"
                @click="editServer(server)"
                class="action-btn edit-btn"
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="deleteServer(server)"
                class="action-btn delete-btn"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </div>
        </div>

        <!-- 桌面端表格视图 -->
        <div class="desktop-view" v-else>
          <el-table :data="serverList" class="server-table">
            <el-table-column prop="server_name" label="服务器名称" min-width="200">
              <template #default="scope">
                <div class="server-name-cell">
                  <el-icon class="server-icon"><Monitor /></el-icon>
                  <div class="server-info">
                    <div class="server-name-text">{{ scope.row.server_name }}</div>
                    <div class="server-address-text">{{ scope.row.ssh_address }}</div>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="状态" width="120" align="center">
              <template #default="scope">
                <el-tag :type="scope.row.status ? 'success' : 'danger'" class="status-tag">
                  {{ scope.row.status ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column label="磁盘管理" width="140" align="center">
              <template #default="scope">
                <el-button
                  type="primary"
                  @click="viewDiskSpace(scope.row)"
                  class="management-btn disk-management-btn"
                >
                  <el-icon><FolderOpened /></el-icon>
                  <span>磁盘空间</span>
                </el-button>
              </template>
            </el-table-column>

            <el-table-column label="表空间管理" width="140" align="center">
              <template #default="scope">
                <el-button
                  type="success"
                  @click="viewTablespace(scope.row)"
                  class="management-btn tablespace-management-btn"
                >
                  <el-icon><DataBoard /></el-icon>
                  <span>表空间</span>
                </el-button>
              </template>
            </el-table-column>

            <el-table-column label="服务器操作" width="160" align="center">
              <template #default="scope">
                <div class="server-operations">
                  <el-button
                    size="small"
                    type="info"
                    @click="editServer(scope.row)"
                    class="operation-btn"
                  >
                    <el-icon><Edit /></el-icon>
                    <span>编辑</span>
                  </el-button>
                  <el-button
                    size="small"
                    type="danger"
                    @click="deleteServer(scope.row)"
                    class="operation-btn"
                  >
                    <el-icon><Delete /></el-icon>
                    <span>删除</span>
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="磁盘空间" name="diskspace" v-if="selectedServer">
        <div class="space-header">
          <div class="server-title">
            <el-icon class="title-icon"><Monitor /></el-icon>
            <span class="server-name-title">{{ selectedServer.server_name }}</span>
            <el-tag type="info" class="server-address-tag">{{ selectedServer.ssh_address }}</el-tag>
          </div>
          <el-button
            type="primary"
            @click="refreshDiskSpace"
            :loading="loading"
            class="refresh-btn"
          >
            <el-icon><Refresh /></el-icon>
            <span class="btn-text">刷新</span>
          </el-button>
        </div>

        <!-- 移动端磁盘空间卡片视图 -->
        <div class="mobile-view" v-if="isMobile">
          <div
            v-for="disk in diskSpaceInfo"
            :key="disk.filesystem"
            class="disk-card"
          >
            <div class="disk-header">
              <div class="disk-filesystem">
                <el-icon class="disk-icon"><FolderOpened /></el-icon>
                {{ disk.filesystem }}
              </div>
              <div class="disk-mount">{{ disk.mounted_on }}</div>
            </div>

            <div class="disk-usage">
              <div class="usage-info">
                <div class="usage-item">
                  <span class="usage-label">总大小:</span>
                  <span class="usage-value">{{ disk.size }}</span>
                </div>
                <div class="usage-item">
                  <span class="usage-label">已用:</span>
                  <span class="usage-value used">{{ disk.used }}</span>
                </div>
                <div class="usage-item">
                  <span class="usage-label">可用:</span>
                  <span class="usage-value available">{{ disk.available }}</span>
                </div>
              </div>

              <div class="usage-progress">
                <el-progress
                  :percentage="parseFloat(disk.use_percent)"
                  :status="parseFloat(disk.use_percent) > 90 ? 'exception' : parseFloat(disk.use_percent) > 80 ? 'warning' : 'success'"
                  :stroke-width="8"
                  class="disk-progress"
                />
                <div class="progress-text">{{ disk.use_percent }}% 已使用</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 桌面端磁盘空间表格视图 -->
        <div class="desktop-view" v-else>
          <el-table :data="diskSpaceInfo" class="disk-table">
            <el-table-column prop="filesystem" label="文件系统" min-width="200">
              <template #default="scope">
                <div class="filesystem-cell">
                  <el-icon class="fs-icon"><FolderOpened /></el-icon>
                  <div class="fs-info">
                    <div class="fs-name">{{ scope.row.filesystem }}</div>
                    <div class="fs-mount">{{ scope.row.mounted_on }}</div>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="存储空间" min-width="300" align="center">
              <template #default="scope">
                <div class="storage-info">
                  <div class="storage-stats">
                    <div class="storage-item">
                      <span class="storage-label">总大小</span>
                      <span class="storage-value total">{{ scope.row.size }}</span>
                    </div>
                    <div class="storage-item">
                      <span class="storage-label">已用</span>
                      <span class="storage-value used">{{ scope.row.used }}</span>
                    </div>
                    <div class="storage-item">
                      <span class="storage-label">可用</span>
                      <span class="storage-value available">{{ scope.row.available }}</span>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="使用率" min-width="250" align="center">
              <template #default="scope">
                <div class="usage-progress-desktop">
                  <el-progress
                    :percentage="parseFloat(scope.row.use_percent)"
                    :status="parseFloat(scope.row.use_percent) > 90 ? 'exception' : parseFloat(scope.row.use_percent) > 80 ? 'warning' : 'success'"
                    :stroke-width="8"
                    class="disk-progress-bar"
                  />
                  <div class="progress-info">
                    <span class="progress-percentage">{{ scope.row.use_percent }}%</span>
                    <span class="progress-status" :class="{
                      'status-danger': parseFloat(scope.row.use_percent) > 90,
                      'status-warning': parseFloat(scope.row.use_percent) > 80 && parseFloat(scope.row.use_percent) <= 90,
                      'status-success': parseFloat(scope.row.use_percent) <= 80
                    }">
                      {{ parseFloat(scope.row.use_percent) > 90 ? '空间不足' :
                         parseFloat(scope.row.use_percent) > 80 ? '空间紧张' : '空间充足' }}
                    </span>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="表空间管理" name="tablespace" v-if="selectedServer">
        <div class="space-header">
          <div class="server-title">
            <el-icon class="title-icon"><DataBoard /></el-icon>
            <span class="server-name-title">{{ selectedServer.server_name }}</span>
            <el-tag type="info" class="server-address-tag">{{ selectedServer.ssh_address }}</el-tag>
          </div>
          <el-button
            type="primary"
            @click="refreshTablespace"
            :loading="loading"
            class="refresh-btn"
          >
            <el-icon><Refresh /></el-icon>
            <span class="btn-text">刷新</span>
          </el-button>
        </div>

        <!-- 移动端表空间卡片视图 -->
        <div class="mobile-view" v-if="isMobile">
          <div
            v-for="tablespace in tablespaceInfo"
            :key="tablespace.tablespace_name"
            class="tablespace-card"
            @click="viewDatafiles(tablespace)"
          >
            <div class="tablespace-header">
              <div class="tablespace-name">
                <el-icon class="tablespace-icon"><DataBoard /></el-icon>
                {{ tablespace.tablespace_name }}
              </div>
              <el-button
                size="small"
                type="primary"
                @click.stop="showExtendDialog(tablespace)"
                class="extend-btn"
              >
                <el-icon><Plus /></el-icon>
                扩容
              </el-button>
            </div>

            <div class="tablespace-usage">
              <div class="usage-stats">
                <div class="stat-item">
                  <span class="stat-label">总大小:</span>
                  <span class="stat-value">{{ formatSize(tablespace.total_size_mb) }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">已用:</span>
                  <span class="stat-value used">{{ formatSize(tablespace.used_size_mb) }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">可用:</span>
                  <span class="stat-value available">{{ formatSize(tablespace.free_size_mb) }}</span>
                </div>
              </div>

              <div class="usage-progress">
                <el-progress
                  :percentage="parseFloat(tablespace.use_percent)"
                  :status="parseFloat(tablespace.use_percent) > 90 ? 'exception' : parseFloat(tablespace.use_percent) > 80 ? 'warning' : 'success'"
                  :stroke-width="8"
                  class="tablespace-progress"
                />
                <div class="progress-text">{{ tablespace.use_percent.toFixed(1) }}% 已使用</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 桌面端表空间表格视图 -->
        <div class="desktop-view" v-else>
          <el-table
            :data="tablespaceInfo"
            class="tablespace-table"
            @row-click="viewDatafiles"
          >
            <el-table-column prop="tablespace_name" label="表空间名称" min-width="200">
              <template #default="scope">
                <div class="tablespace-name-cell">
                  <el-icon class="ts-icon"><DataBoard /></el-icon>
                  <div class="ts-info">
                    <div class="ts-name">{{ scope.row.tablespace_name }}</div>
                    <div class="ts-hint">点击查看数据文件详情</div>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="空间使用情况" min-width="350" align="center">
              <template #default="scope">
                <div class="tablespace-usage-info">
                  <div class="usage-stats-desktop">
                    <div class="usage-stat-item">
                      <span class="stat-label">总大小</span>
                      <span class="stat-value total">{{ formatSize(scope.row.total_size_mb) }}</span>
                    </div>
                    <div class="usage-stat-item">
                      <span class="stat-label">已用</span>
                      <span class="stat-value used">{{ formatSize(scope.row.used_size_mb) }}</span>
                    </div>
                    <div class="usage-stat-item">
                      <span class="stat-label">可用</span>
                      <span class="stat-value available">{{ formatSize(scope.row.free_size_mb) }}</span>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="使用率分析" min-width="280" align="center">
              <template #default="scope">
                <div class="usage-analysis">
                  <el-progress
                    :percentage="parseFloat(scope.row.use_percent)"
                    :status="parseFloat(scope.row.use_percent) > 90 ? 'exception' : parseFloat(scope.row.use_percent) > 80 ? 'warning' : 'success'"
                    :stroke-width="8"
                    class="tablespace-progress-bar"
                  />
                  <div class="analysis-info">
                    <span class="analysis-percentage">{{ scope.row.use_percent.toFixed(1) }}%</span>
                    <span class="analysis-status" :class="{
                      'status-danger': parseFloat(scope.row.use_percent) > 90,
                      'status-warning': parseFloat(scope.row.use_percent) > 80 && parseFloat(scope.row.use_percent) <= 90,
                      'status-success': parseFloat(scope.row.use_percent) <= 80
                    }">
                      {{ parseFloat(scope.row.use_percent) > 90 ? '需要扩容' :
                         parseFloat(scope.row.use_percent) > 80 ? '建议扩容' : '空间充足' }}
                    </span>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="扩容操作" width="140" align="center">
              <template #default="scope">
                <el-button
                  type="primary"
                  @click.stop="showExtendDialog(scope.row)"
                  class="extend-btn-desktop"
                  :class="{
                    'urgent-extend': parseFloat(scope.row.use_percent) > 90,
                    'suggest-extend': parseFloat(scope.row.use_percent) > 80 && parseFloat(scope.row.use_percent) <= 90
                  }"
                >
                  <el-icon><Plus /></el-icon>
                  <span>{{ parseFloat(scope.row.use_percent) > 90 ? '紧急扩容' : '扩容' }}</span>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 数据文件信息 -->
        <div v-if="selectedTablespace" class="datafiles-section">
          <div class="datafiles-header">
            <div class="datafiles-title">
              <el-icon class="datafiles-icon"><Document /></el-icon>
              <span>{{ selectedTablespace.tablespace_name }} 的数据文件</span>
            </div>
            <el-button
              size="small"
              @click="refreshDatafiles"
              class="refresh-datafiles-btn"
            >
              <el-icon><Refresh /></el-icon>
              <span class="btn-text">刷新</span>
            </el-button>
          </div>

          <!-- 移动端数据文件卡片视图 -->
          <div class="mobile-view" v-if="isMobile">
            <div
              v-for="file in datafilesInfo"
              :key="file.file_name"
              class="datafile-card"
            >
              <div class="datafile-header">
                <div class="datafile-name">
                  <el-icon class="file-icon"><Document /></el-icon>
                  {{ getFileName(file.file_name) }}
                </div>
                <el-tag
                  :type="file.autoextensible === 'YES' ? 'success' : 'info'"
                  class="auto-extend-tag"
                >
                  {{ file.autoextensible === 'YES' ? '自动扩展' : '固定大小' }}
                </el-tag>
              </div>

              <div class="datafile-info">
                <div class="file-stat">
                  <span class="file-label">当前大小:</span>
                  <span class="file-value">{{ formatSize(file.file_size_mb) }}</span>
                </div>
                <div class="file-stat" v-if="file.max_size_mb && file.max_size_mb !== 'UNLIMITED'">
                  <span class="file-label">最大大小:</span>
                  <span class="file-value">{{ formatSize(file.max_size_mb) }}</span>
                </div>
                <div class="file-path">{{ file.file_name }}</div>
              </div>
            </div>
          </div>

          <!-- 桌面端数据文件表格视图 -->
          <div class="desktop-view" v-else>
            <el-table :data="datafilesInfo" class="datafiles-table">
              <el-table-column prop="file_name" label="数据文件信息" min-width="350">
                <template #default="scope">
                  <div class="filename-cell">
                    <el-icon class="file-icon"><Document /></el-icon>
                    <div class="file-info">
                      <div class="file-name">{{ getFileName(scope.row.file_name) }}</div>
                      <div class="file-path">{{ scope.row.file_name }}</div>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="文件大小" min-width="200" align="center">
                <template #default="scope">
                  <div class="file-size-info">
                    <div class="size-item">
                      <span class="size-label">当前大小</span>
                      <span class="size-value current">{{ formatSize(scope.row.file_size_mb) }}</span>
                    </div>
                    <div class="size-item" v-if="scope.row.max_size_mb && scope.row.max_size_mb !== 'UNLIMITED'">
                      <span class="size-label">最大大小</span>
                      <span class="size-value max">{{ formatSize(scope.row.max_size_mb) }}</span>
                    </div>
                    <div class="size-item" v-else>
                      <span class="size-label">最大大小</span>
                      <span class="size-value unlimited">无限制</span>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="扩展配置" width="180" align="center">
                <template #default="scope">
                  <div class="extend-config">
                    <el-tag
                      :type="scope.row.autoextensible === 'YES' ? 'success' : 'info'"
                      class="extend-tag"
                    >
                      <el-icon>
                        <component :is="scope.row.autoextensible === 'YES' ? 'Check' : 'Close'" />
                      </el-icon>
                      {{ scope.row.autoextensible === 'YES' ? '自动扩展' : '固定大小' }}
                    </el-tag>
                    <div class="extend-hint">
                      {{ scope.row.autoextensible === 'YES' ? '文件可自动增长' : '文件大小固定' }}
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    
    <!-- 添加/编辑服务器对话框 -->
    <el-dialog 
      :title="dialogType === 'add' ? '添加服务器' : '编辑服务器'" 
      v-model="serverDialogVisible"
      width="500px"
    >
      <el-form :model="serverForm" label-width="100px" :rules="serverRules" ref="serverFormRef">
        <el-form-item label="服务器名称" prop="server_name">
          <el-input v-model="serverForm.server_name" placeholder="请输入服务器名称"></el-input>
        </el-form-item>
        <el-form-item label="SSH地址" prop="ssh_address">
          <el-input v-model="serverForm.ssh_address" placeholder="请输入SSH地址"></el-input>
        </el-form-item>
        <el-form-item label="SSH端口" prop="ssh_port">
          <el-input-number v-model="serverForm.ssh_port" :min="1" :max="65535" placeholder="请输入SSH端口"></el-input-number>
        </el-form-item>
        <el-form-item label="SSH用户名" prop="ssh_username">
          <el-input v-model="serverForm.ssh_username" placeholder="请输入SSH用户名"></el-input>
        </el-form-item>
        <el-form-item label="数据库地址" prop="db_address">
          <el-input v-model="serverForm.db_address" placeholder="请输入数据库地址"></el-input>
        </el-form-item>
        <el-form-item label="数据库端口" prop="db_port">
          <el-input-number v-model="serverForm.db_port" :min="1" :max="65535" placeholder="请输入数据库端口"></el-input-number>
        </el-form-item>
        <el-form-item label="数据库用户名" prop="db_username">
          <el-input v-model="serverForm.db_username" placeholder="请输入数据库用户名"></el-input>
        </el-form-item>
        <el-form-item label="数据库密码" prop="db_password">
          <el-input v-model="serverForm.db_password" type="password" placeholder="请输入数据库密码"></el-input>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-switch v-model="serverForm.status" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="serverDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitServerForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 扩容表空间对话框 -->
    <el-dialog 
      title="扩容表空间" 
      v-model="extendDialogVisible"
      width="500px"
    >
      <el-form :model="extendForm" label-width="120px" :rules="extendRules" ref="extendFormRef">
        <el-form-item label="表空间名称">
          <el-input v-model="extendForm.tablespace_name" disabled></el-input>
        </el-form-item>
        <el-form-item label="数据文件" prop="datafile_name">
          <el-select v-model="extendForm.datafile_name" placeholder="请选择数据文件">
            <el-option
              v-for="file in datafilesInfo"
              :key="file.file_name"
              :label="file.file_name"
              :value="file.file_name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="扩容大小(MB)" prop="size_mb">
          <el-input-number v-model="extendForm.size_mb" :min="1" :step="256"></el-input-number>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="extendDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitExtendForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'
import {
  Plus, Monitor, FolderOpened, DataBoard, Edit, Delete,
  Refresh, Document, Check, Close
} from '@element-plus/icons-vue'

export default {
  name: 'OracleManage',
  components: {
    Plus, Monitor, FolderOpened, DataBoard, Edit, Delete,
    Refresh, Document, Check, Close
  },
  setup() {
    // 状态变量
    const activeTab = ref('servers') // 默认显示服务器管理标签页
    const serverList = ref([])
    const selectedServer = ref(null)
    const diskSpaceInfo = ref([])
    const tablespaceInfo = ref([])
    const selectedTablespace = ref(null)
    const datafilesInfo = ref([])
    const isMobile = ref(false)
    const loading = ref(false)
    
    // 对话框控制
    const serverDialogVisible = ref(false)
    const extendDialogVisible = ref(false)
    const dialogType = ref('add')
    
    // 表单数据
    const serverForm = reactive({
      server_name: '',
      ssh_address: '',
      ssh_port: 22,
      ssh_username: 'root',
      db_address: '',
      db_port: 1521,
      db_username: 'paymentdb',
      db_password: '',
      status: true
    })
    
    const extendForm = reactive({
      server_id: '',
      tablespace_name: '',
      datafile_name: '',
      size_mb: 1024
    })
    
    // 表单验证规则
    const serverRules = {
      server_name: [{ required: true, message: '请输入服务器名称', trigger: 'blur' }],
      ssh_address: [{ required: true, message: '请输入SSH地址', trigger: 'blur' }],
      ssh_port: [{ required: true, message: '请输入SSH端口', trigger: 'blur' }],
      ssh_username: [{ required: true, message: '请输入SSH用户名', trigger: 'blur' }],
      db_address: [{ required: true, message: '请输入数据库地址', trigger: 'blur' }],
      db_port: [{ required: true, message: '请输入数据库端口', trigger: 'blur' }],
      db_username: [{ required: true, message: '请输入数据库用户名', trigger: 'blur' }],
      db_password: [{ required: true, message: '请输入数据库密码', trigger: 'blur' }]
    }
    
    const extendRules = {
      datafile_name: [{ required: true, message: '请选择数据文件', trigger: 'change' }],
      size_mb: [{ required: true, message: '请输入扩容大小', trigger: 'blur' }]
    }
    
    const serverFormRef = ref(null)
    const extendFormRef = ref(null)

    // 检测是否为移动设备
    const checkMobile = () => {
      isMobile.value = window.innerWidth <= 768
    }

    // 监听窗口大小变化
    const handleResize = () => {
      checkMobile()
    }

    // 格式化大小显示
    const formatSize = (sizeInMB) => {
      if (!sizeInMB || sizeInMB === 'UNLIMITED') return sizeInMB
      const size = parseFloat(sizeInMB)
      if (size >= 1024) {
        return `${(size / 1024).toFixed(1)} GB`
      }
      return `${size.toFixed(0)} MB`
    }

    // 获取文件名（去掉路径）
    const getFileName = (fullPath) => {
      if (!fullPath) return ''
      const parts = fullPath.split(/[/\\]/)
      return parts[parts.length - 1]
    }

    // 初始化
    onMounted(() => {
      checkMobile()
      window.addEventListener('resize', handleResize)
      fetchServerList()
    })

    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
    })
    
    // 获取服务器列表
    const fetchServerList = async () => {
      try {
        const response = await axios.get(`/api/oracle/servers`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })
        if (response.data.code === 200) {
          if (Array.isArray(response.data.data)) {
            serverList.value = response.data.data.map(server => ({
              ...server,
              ip_address: server.ip_address || server.ssh_address || ''
            }))
          } else {
            serverList.value = []
            console.error('服务器列表数据格式错误')
          }
        } else {
          ElMessage.error(response.data.message || '获取服务器列表失败')
        }
      } catch (error) {
        console.error('获取服务器列表出错:', error)
        ElMessage.error('获取服务器列表失败: ' + (error.response?.data?.message || error.message))
      }
    }
    
    // 查看磁盘空间
    const viewDiskSpace = async (server) => {
      selectedServer.value = server
      activeTab.value = 'diskspace'
      await fetchDiskSpace(server)
    }
    
    // 获取磁盘空间信息
    const fetchDiskSpace = async (server) => {
      loading.value = true
      try {
        const response = await axios.get(`/api/oracle/disk_space`, {
          params: { server_id: server.id },
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })
        if (response.data.success) {
          // 直接使用返回的磁盘空间信息数组
          diskSpaceInfo.value = response.data.data.disk_space_info.map(item => ({
            filesystem: item.device,
            size: item.total,
            used: item.used,
            available: item.available,
            use_percent: item.usage.replace('%', ''), // 移除百分号以便进度条使用
            mounted_on: item.mount_point
          }))
        } else {
          ElMessage.error(response.data.message || '获取磁盘空间信息失败')
        }
      } catch (error) {
        console.error('获取磁盘空间信息出错:', error)
        ElMessage.error('获取磁盘空间信息失败')
      } finally {
        loading.value = false
      }
    }
    
    // 刷新磁盘空间信息
    const refreshDiskSpace = () => {
      if (selectedServer.value) {
        fetchDiskSpace(selectedServer.value)
      }
    }
    
    // 查看表空间
    const viewTablespace = async (server) => {
      selectedServer.value = server
      activeTab.value = 'tablespace'
      await fetchTablespace(server)
    }
    
    // 获取表空间信息
    const fetchTablespace = async (server) => {
      loading.value = true
      try {
        const response = await axios.get(`/api/oracle/tablespace`, {
          params: { server_id: server.id },
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })
        if (response.data.success) {
          // 处理新的API返回格式
          const tablespaceData = response.data.data.tablespace_info

          // 检查tablespaceData是否为数组
          if (Array.isArray(tablespaceData)) {
            // 如果是数组，直接映射处理每个表空间
            tablespaceInfo.value = tablespaceData.map(ts => ({
              tablespace_name: ts.tablespace_name,
              total_size_mb: ts.total_size_mb,
              used_size_mb: ts.used_size_mb,
              free_size_mb: ts.free_size_mb,
              use_percent: 100 - ts.free_percent, // 使用率 = 100 - 空闲百分比
              datafiles: ts.datafiles // 保存数据文件信息，以便后续使用
            }))
            // 如果有表空间数据，自动选择第一个表空间并显示其数据文件
            if (tablespaceInfo.value.length > 0) {
              selectedTablespace.value = tablespaceInfo.value[0]
              datafilesInfo.value = selectedTablespace.value.datafiles.map(file => ({
                file_name: file.file_name,
                file_size_mb: file.size_mb,
                autoextensible: file.auto_extend,
                max_size_mb: file.max_size_mb
              }))
            }
          } else {
            // 如果不是数组，按照原来的方式处理单个表空间
            tablespaceInfo.value = [{
              tablespace_name: tablespaceData.tablespace_name,
              total_size_mb: tablespaceData.total_size_mb,
              used_size_mb: tablespaceData.used_size_mb,
              free_size_mb: tablespaceData.free_size_mb,
              use_percent: 100 - tablespaceData.free_percent, // 使用率 = 100 - 空闲百分比
              datafiles: tablespaceData.datafiles // 保存数据文件信息，以便后续使用
            }]
            // 自动选择这个表空间并显示其数据文件
            selectedTablespace.value = tablespaceInfo.value[0]
            datafilesInfo.value = selectedTablespace.value.datafiles.map(file => ({
              file_name: file.file_name,
              file_size_mb: file.size_mb,
              autoextensible: file.auto_extend,
              max_size_mb: file.max_size_mb
            }))
          }
        } else {
          ElMessage.error(response.data.message || '获取表空间信息失败')
        }
      } catch (error) {
        console.error('获取表空间信息出错:', error)
        ElMessage.error('获取表空间信息失败')
      } finally {
        loading.value = false
      }
    }
    
    // 刷新表空间信息
    const refreshTablespace = () => {
      if (selectedServer.value) {
        fetchTablespace(selectedServer.value)
      }
    }
    
    // 查看数据文件
    const viewDatafiles = (tablespace) => {
      selectedTablespace.value = tablespace
      // 直接使用表空间信息中的数据文件信息
      datafilesInfo.value = tablespace.datafiles.map(file => ({
        file_name: file.file_name,
        file_size_mb: file.size_mb,
        autoextensible: file.auto_extend,
        max_size_mb: file.max_size_mb
      }))
    }
    
    // 刷新数据文件信息
    const refreshDatafiles = () => {
      refreshDatafilesForTablespace()
    }
    
    // 显示添加服务器对话框
    const showAddServerDialog = () => {
      dialogType.value = 'add'
      serverForm.server_name = ''
      serverForm.ssh_address = ''
      serverForm.ssh_port = 22
      serverForm.ssh_username = 'root'
      serverForm.db_address = ''
      serverForm.db_port = 1521
      serverForm.db_username = 'paymentdb'
      serverForm.db_password = ''
      serverForm.status = true
      serverDialogVisible.value = true
    }
    
    // 编辑服务器
    const editServer = (server) => {
      dialogType.value = 'edit'
      serverForm.id = server.id
      serverForm.server_name = server.server_name
      serverForm.ssh_address = server.ssh_address
      serverForm.ssh_port = server.ssh_port
      serverForm.ssh_username = server.ssh_username
      serverForm.db_address = server.db_address
      serverForm.db_port = server.db_port
      serverForm.db_username = server.db_username
      serverForm.db_password = ''
      serverForm.status = server.status
      serverDialogVisible.value = true
    }
    
    // 提交服务器表单
    const submitServerForm = async () => {
      if (!serverFormRef.value) return
      
      await serverFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            let response
            if (dialogType.value === 'add') {
              response = await axios.post(`/api/oracle/servers`, serverForm, {
                headers: {
                  'Authorization': `Bearer ${localStorage.getItem('token')}`,
                  'Content-Type': 'application/json'
                }
              })
            } else {
              response = await axios.put(`/api/oracle/servers/${serverForm.id}`, {
                server_name: serverForm.server_name,
                ssh_address: serverForm.ssh_address,
                ssh_port: serverForm.ssh_port,
                ssh_username: serverForm.ssh_username,
                db_address: serverForm.db_address,
                db_port: serverForm.db_port,
                db_username: serverForm.db_username,
                db_password: serverForm.db_password,
                status: serverForm.status
              }, {
                headers: {
                  'Authorization': `Bearer ${localStorage.getItem('token')}`,
                  'Content-Type': 'application/json'
                }
              })
            }
            
            if (response.data.success) {
              ElMessage.success(response.data.message || '操作成功')
              serverDialogVisible.value = false
              fetchServerList()
            } else {
              ElMessage.error(response.data.message || '操作失败')
            }
          } catch (error) {
            console.error('提交服务器表单出错:', error)
            ElMessage.error('操作失败')
          }
        }
      })
    }
    
    // 删除服务器
    const deleteServer = (server) => {
      ElMessageBox.confirm(
        `确定要删除服务器 ${server.server_name} 吗？`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        try {
          const response = await axios.delete(`/api/oracle/servers/${server.id}`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          })
          if (response.data.success) {
            ElMessage.success(response.data.message || '删除成功')
            fetchServerList()
          } else {
            ElMessage.error(response.data.message || '删除失败')
          }
        } catch (error) {
          console.error('删除服务器出错:', error)
          ElMessage.error('删除失败')
        }
      }).catch(() => {})
    }
    
    // 显示扩容对话框
    const showExtendDialog = (tablespace) => {
      extendForm.server_id = selectedServer.value.id
      extendForm.tablespace_name = tablespace.tablespace_name
      extendForm.datafile_name = ''
      extendForm.size_mb = 1024
      // 更新当前表空间的数据文件列表
      viewDatafiles(tablespace)
      extendDialogVisible.value = true
    }
    
    // 提交扩容表单
const submitExtendForm = async () => {
  if (!extendFormRef.value) return
  
  await extendFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const response = await axios.post(`/api/oracle/extend_tablespace`, extendForm, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        })
        
        if (response.data.success) {
          ElMessage.success(response.data.message || '扩容成功')
          extendDialogVisible.value = false
          // 刷新表空间信息
          fetchTablespace(selectedServer.value)
        } else {
          ElMessage.error(response.data.message || '扩容失败')
        }
      } catch (error) {
        console.error('扩容表空间出错:', error)
        ElMessage.error('扩容失败')
      }
    }
  })
}
    
    // 刷新数据文件信息（直接在表空间页面内显示）
    const refreshDatafilesForTablespace = () => {
      if (selectedServer.value && selectedTablespace.value) {
        // 刷新表空间信息，数据文件信息会随之更新
        fetchTablespace(selectedServer.value)
        // 从刷新后的表空间信息中找到当前查看的表空间
        const currentTablespace = tablespaceInfo.value.find(
          ts => ts.tablespace_name === selectedTablespace.value.tablespace_name
        )
        if (currentTablespace) {
          viewDatafiles(currentTablespace)
        }
      }
    }
    
    return {
      activeTab,
      serverList,
      selectedServer,
      diskSpaceInfo,
      tablespaceInfo,
      selectedTablespace,
      datafilesInfo,
      isMobile,
      loading,
      serverDialogVisible,
      extendDialogVisible,
      dialogType,
      serverForm,
      extendForm,
      serverRules,
      extendRules,
      serverFormRef,
      extendFormRef,
      formatSize,
      getFileName,
      viewDiskSpace,
      refreshDiskSpace,
      viewTablespace,
      refreshTablespace,
      viewDatafiles,
      refreshDatafiles,
      showAddServerDialog,
      editServer,
      submitServerForm,
      deleteServer,
      showExtendDialog,
      submitExtendForm
    }
  }
}
</script>

<style scoped>
.oracle-manage {
  padding: 20px;
  min-height: 100vh;
  background: #f8f9fa;
}

/* 响应式容器 */
@media (max-width: 768px) {
  .oracle-manage {
    padding: 10px;
  }
}

/* 标签页样式 */
.oracle-tabs {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

:deep(.el-tabs__header) {
  margin: 0;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-bottom: 1px solid #e9ecef;
}

:deep(.el-tabs__content) {
  padding: 20px;
}

@media (max-width: 768px) {
  :deep(.el-tabs__content) {
    padding: 15px;
  }
}

/* 操作栏样式 */
.action-bar {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
}

.add-server-btn {
  border-radius: 8px;
  font-weight: 500;
  padding: 10px 20px;
  background: linear-gradient(135deg, #409eff, #66b1ff);
  border-color: #409eff;
}

@media (max-width: 480px) {
  .add-server-btn {
    width: 100%;
    justify-content: center;
  }

  .btn-text {
    margin-left: 4px;
  }
}

/* 移动端卡片视图 */
.mobile-view {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 服务器卡片样式 */
.server-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.server-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.server-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.server-name {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-tag {
  font-weight: 500;
  border-radius: 20px;
}

.server-info-mobile {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
  font-size: 14px;
}

.info-icon {
  color: #909399;
}

.info-label {
  font-weight: 500;
}

.info-value {
  color: #303133;
  font-family: 'Monaco', 'Menlo', monospace;
}

.server-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.action-btn {
  border-radius: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 8px 12px;
}

.disk-btn {
  background: linear-gradient(135deg, #409eff, #66b1ff);
  border-color: #409eff;
}

.tablespace-btn {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  border-color: #67c23a;
}

.edit-btn {
  background: linear-gradient(135deg, #909399, #b1b3b8);
  border-color: #909399;
}

.delete-btn {
  background: linear-gradient(135deg, #f56c6c, #f78989);
  border-color: #f56c6c;
}

/* 桌面端表格视图 */
.desktop-view {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.server-table {
  border-radius: 8px;
}

.server-name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
}

.server-icon {
  color: #409eff;
  font-size: 20px;
}

.server-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.server-name-text {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.server-address-text {
  font-size: 12px;
  color: #909399;
  font-family: 'Monaco', 'Menlo', monospace;
}

/* 管理按钮样式 */
.management-btn {
  border-radius: 8px;
  font-weight: 500;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 110px;
  justify-content: center;
}

.disk-management-btn {
  background: linear-gradient(135deg, #409eff, #66b1ff);
  border-color: #409eff;
}

.tablespace-management-btn {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  border-color: #67c23a;
}

/* 服务器操作按钮 */
.server-operations {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.operation-btn {
  border-radius: 6px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 70px;
  justify-content: center;
}

/* 空间管理头部样式 */
.space-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.server-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 20px;
  color: #409eff;
}

.server-name-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.server-address-tag {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  border-radius: 12px;
}

.refresh-btn {
  border-radius: 8px;
  font-weight: 500;
  background: linear-gradient(135deg, #409eff, #66b1ff);
  border-color: #409eff;
}

@media (max-width: 768px) {
  .space-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .refresh-btn {
    width: 100%;
    justify-content: center;
  }
}

/* 磁盘空间卡片样式 */
.disk-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.disk-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.disk-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.disk-filesystem {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.disk-icon {
  color: #409eff;
  font-size: 18px;
}

.disk-mount {
  font-size: 12px;
  color: #909399;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 12px;
}

.disk-usage {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.usage-info {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
}

.usage-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.usage-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.usage-value {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.usage-value.used {
  color: #e6a23c;
}

.usage-value.available {
  color: #67c23a;
}

.usage-progress {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.disk-progress {
  margin: 0;
}

.progress-text {
  text-align: center;
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

/* 桌面端磁盘表格样式 */
.disk-table {
  border-radius: 8px;
}

.filesystem-cell {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
}

.fs-icon {
  color: #409eff;
  font-size: 20px;
}

.fs-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.fs-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.fs-mount {
  font-size: 12px;
  color: #909399;
  font-family: 'Monaco', 'Menlo', monospace;
}

/* 存储信息样式 */
.storage-info {
  padding: 8px 0;
}

.storage-stats {
  display: flex;
  justify-content: space-around;
  gap: 20px;
}

.storage-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.storage-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.storage-value {
  font-size: 14px;
  font-weight: 600;
}

.storage-value.total {
  color: #303133;
}

.storage-value.used {
  color: #e6a23c;
}

.storage-value.available {
  color: #67c23a;
}

/* 使用率进度条样式 */
.usage-progress-desktop {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px 0;
}

.disk-progress-bar {
  margin: 0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-percentage {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.progress-status {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 12px;
}

.progress-status.status-success {
  color: #67c23a;
  background: #f0f9ff;
}

.progress-status.status-warning {
  color: #e6a23c;
  background: #fdf6ec;
}

.progress-status.status-danger {
  color: #f56c6c;
  background: #fef0f0;
}

/* 表空间卡片样式 */
.tablespace-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

.tablespace-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
  border-color: #409eff;
}

.tablespace-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.tablespace-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.tablespace-icon {
  color: #67c23a;
  font-size: 18px;
}

.extend-btn {
  border-radius: 8px;
  font-weight: 500;
  background: linear-gradient(135deg, #409eff, #66b1ff);
  border-color: #409eff;
}

.tablespace-usage {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.usage-stats {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.stat-value.used {
  color: #e6a23c;
}

.stat-value.available {
  color: #67c23a;
}

.tablespace-progress {
  margin: 0;
}

/* 桌面端表空间表格样式 */
.tablespace-table {
  border-radius: 8px;
  margin-bottom: 20px;
}

.tablespace-table :deep(.el-table__row) {
  cursor: pointer;
}

.tablespace-table :deep(.el-table__row:hover) {
  background-color: #f8f9fa;
}

.tablespace-name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
}

.ts-icon {
  color: #67c23a;
  font-size: 20px;
}

.ts-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.ts-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.ts-hint {
  font-size: 12px;
  color: #c0c4cc;
}

/* 表空间使用情况样式 */
.tablespace-usage-info {
  padding: 8px 0;
}

.usage-stats-desktop {
  display: flex;
  justify-content: space-around;
  gap: 20px;
}

.usage-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.usage-stat-item .stat-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.usage-stat-item .stat-value {
  font-size: 14px;
  font-weight: 600;
}

.usage-stat-item .stat-value.total {
  color: #303133;
}

.usage-stat-item .stat-value.used {
  color: #e6a23c;
}

.usage-stat-item .stat-value.available {
  color: #67c23a;
}

/* 使用率分析样式 */
.usage-analysis {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px 0;
}

.tablespace-progress-bar {
  margin: 0;
}

.analysis-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.analysis-percentage {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.analysis-status {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 12px;
}

.analysis-status.status-success {
  color: #67c23a;
  background: #f0f9ff;
}

.analysis-status.status-warning {
  color: #e6a23c;
  background: #fdf6ec;
}

.analysis-status.status-danger {
  color: #f56c6c;
  background: #fef0f0;
}

/* 扩容按钮样式 */
.extend-btn-desktop {
  border-radius: 8px;
  font-weight: 500;
  padding: 8px 16px;
  background: linear-gradient(135deg, #409eff, #66b1ff);
  border-color: #409eff;
  display: flex;
  align-items: center;
  gap: 6px;
  justify-content: center;
  min-width: 100px;
}

.extend-btn-desktop.suggest-extend {
  background: linear-gradient(135deg, #e6a23c, #eebe77);
  border-color: #e6a23c;
}

.extend-btn-desktop.urgent-extend {
  background: linear-gradient(135deg, #f56c6c, #f78989);
  border-color: #f56c6c;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(245, 108, 108, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0);
  }
}

/* 数据文件区域样式 */
.datafiles-section {
  margin-top: 24px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.datafiles-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.datafiles-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.datafiles-icon {
  color: #909399;
  font-size: 18px;
}

.refresh-datafiles-btn {
  border-radius: 6px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .datafiles-header {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .refresh-datafiles-btn {
    width: 100%;
    justify-content: center;
  }
}

/* 数据文件卡片样式 */
.datafile-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.3s ease;
}

.datafile-card:hover {
  background: #f5f7fa;
  border-color: #d3d4d6;
}

.datafile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.datafile-name {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.file-icon {
  color: #909399;
  font-size: 16px;
}

.auto-extend-tag {
  font-size: 11px;
  border-radius: 12px;
}

.datafile-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.file-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.file-label {
  color: #909399;
  font-weight: 500;
}

.file-value {
  color: #303133;
  font-weight: 600;
}

.file-path {
  font-size: 11px;
  color: #c0c4cc;
  font-family: 'Monaco', 'Menlo', monospace;
  word-break: break-all;
  margin-top: 4px;
  padding: 4px 8px;
  background: #ffffff;
  border-radius: 4px;
}

/* 桌面端数据文件表格样式 */
.datafiles-table {
  border-radius: 8px;
}

.filename-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  color: #909399;
  font-size: 18px;
}

.file-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.file-path {
  font-size: 11px;
  color: #c0c4cc;
  font-family: 'Monaco', 'Menlo', monospace;
  word-break: break-all;
}

/* 文件大小信息样式 */
.file-size-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px 0;
}

.size-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.size-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.size-value {
  font-size: 13px;
  font-weight: 600;
}

.size-value.current {
  color: #303133;
}

.size-value.max {
  color: #409eff;
}

.size-value.unlimited {
  color: #67c23a;
}

/* 扩展配置样式 */
.extend-config {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.extend-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
  border-radius: 16px;
  padding: 4px 12px;
}

.extend-hint {
  font-size: 11px;
  color: #c0c4cc;
  text-align: center;
}

/* 表格通用样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
  border-bottom: 1px solid #e9ecef;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f8f9fa;
}

/* 进度条样式优化 */
:deep(.el-progress-bar__outer) {
  border-radius: 10px;
}

:deep(.el-progress-bar__inner) {
  border-radius: 10px;
}

/* 标签样式优化 */
:deep(.el-tag) {
  border-radius: 12px;
  font-weight: 500;
  border: none;
}

/* 按钮样式优化 */
:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-bottom: 1px solid #e9ecef;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式优化 */
@media (max-width: 480px) {
  .usage-info,
  .usage-stats {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .usage-item,
  .stat-item {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }

  .server-actions {
    grid-template-columns: 1fr;
  }
}
</style>