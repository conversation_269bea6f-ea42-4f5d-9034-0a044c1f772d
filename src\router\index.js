import { createRouter, createWebHistory } from 'vue-router'
import Login from '../views/Login.vue'
import Dashboard from '../views/Dashboard.vue'

const API_BASE_URL = process.env.VUE_APP_BASE_URL;

// 添加导出语句
export { API_BASE_URL }

// 根据路径和标题获取路由图标
const getRouteIcon = (path, title) => {
  const pathLower = path?.toLowerCase() || ''
  const titleLower = title?.toLowerCase() || ''

  if (pathLower.includes('bluegreen') || titleLower.includes('蓝绿')) return 'Switch'
  if (pathLower.includes('wiki') || titleLower.includes('wiki')) return 'Document'
  if (pathLower.includes('logalert') || titleLower.includes('日志') || titleLower.includes('告警')) return 'Warning'
  if (pathLower.includes('redis') || titleLower.includes('redis')) return 'DataBoard'
  if (pathLower.includes('links') || titleLower.includes('链接')) return 'Link'
  if (pathLower.includes('fail2ban') || titleLower.includes('fail2ban')) return 'Lock'
  if (pathLower.includes('system') || titleLower.includes('系统')) return 'Setting'
  if (pathLower.includes('nginx') || titleLower.includes('nginx')) return 'Tools'
  if (pathLower.includes('database') || titleLower.includes('数据库')) return 'DataBoard'
  if (pathLower.includes('user') || titleLower.includes('用户')) return 'User'
  if (pathLower.includes('sync') || titleLower.includes('同步')) return 'Connection'
  if (pathLower.includes('oracle') || titleLower.includes('oracle')) return 'DataBoard'
  if (pathLower.includes('consistency') || titleLower.includes('一致性')) return 'Files'
  if (pathLower.includes('access') || titleLower.includes('访问')) return 'Key'
  if (pathLower.includes('alertmanager') || titleLower.includes('告警管理')) return 'Bell'
  if (pathLower.includes('dashboard') || titleLower.includes('仪表板')) return 'DataBoard'
  if (pathLower.includes('webhook') || titleLower.includes('webhook')) return 'Connection'
  if (pathLower.includes('silence') || titleLower.includes('静默')) return 'Mute'

  return 'Monitor'
}

// 组件映射配置 - 将路径映射到对应的组件
const componentMap = {
  // 主页面组件
  '/bluegreen': () => import('../views/BlueGreenDeploy.vue'),
  '/wiki_status': () => import('../views/WikiStatus.vue'),
  '/sql_workflow': () => import('../views/SqlWorkflowManage.vue'),
  '/logalert': () => import('../views/LogAlert.vue'),
  '/redis': () => import('../views/RedisManage.vue'),
  '/links': () => import('../views/LinkManage.vue'),
  '/fail2ban': () => import('../views/Fail2ban.vue'),
  '/permission-management/users': () => import('../views/permission/UserList.vue'), // 用户列表的精确路径

  // 权限管理相关组件
  '/permission-management/permissions': () => import('../views/permission/PermissionList.vue'),
  '/permission-management/user-permissions': () => import('../views/permission/UserPermissions.vue'),
  '/permission-management/operation-logs': () => import('../views/permission/OperationLogs.vue'),

  // Nginx 相关组件
  '/nginx/nginx_consistency': () => import('../views/NginxConsistency.vue'),
  '/nginx/access_nginx': () => import('../views/NginxControl.vue'),

  // 数据库相关组件
  '/database/dbsync': () => import('../views/DbSync.vue'),
  '/database/oracle': () => import('../views/OracleManage.vue'),

  // AlertManager 告警管理相关组件
  '/alertmanager/dashboard': () => import('../views/AlertManagerDashboard.vue'),
  '/alertmanager/list': () => import('../views/AlertManagerList.vue'),
  '/alertmanager/detail/:id': () => import('../views/AlertManagerDetail.vue'),
  '/alertmanager/webhook-config': () => import('../views/AlertManagerWebhookConfig.vue'),
  '/alertmanager/silence-rules': () => import('../views/AlertManagerSilenceRules.vue'),
}

// 动态路由生成函数
const generateRoutes = (menuData) => {
  const routes = []

  const processMenuItem = (item) => {
    const fullPath = item.path
    const component = componentMap[fullPath]

    if (!component) {
      console.warn(`未找到路径 ${fullPath} 对应的组件`)
      return null
    }

    // 生成唯一的路由名称，避免冲突
    const routeName = `Route_${item.id}_${item.title.replace(/[^\w\u4e00-\u9fa5]/g, '')}`

    const route = {
      path: fullPath, // 保持原始路径格式，不要移除前导斜杠
      name: routeName,
      component: component,
      meta: {
        requiresAuth: true,
        title: item.title,
        menuId: item.id,
        icon: getRouteIcon(fullPath, item.title),
        breadcrumb: true,
        cache: false // 禁用缓存避免冲突
      }
    }

    return route
  }

  // 处理菜单数据
  menuData.forEach(item => {
    if (item.children && item.children.length > 0) {
      // 有子菜单的情况 - 只处理子菜单，不处理父级菜单
      item.children.forEach(child => {
        const childRoute = processMenuItem(child)
        if (childRoute) {
          routes.push(childRoute)
        }
      })
    } else {
      // 无子菜单的情况 - 处理叶子节点菜单
      const route = processMenuItem(item)
      if (route) {
        routes.push(route)
      }
    }
  })

  return routes
}

// 基础路由配置（不依赖菜单数据的路由）
const baseRoutes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true },
    children: [] // 动态路由将添加到这里
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes: baseRoutes
})

// 存储已添加的路由名称，避免重复添加
const addedRouteNames = new Set()

// 动态添加路由的函数
export const addDynamicRoutes = async () => {
  try {
    const response = await fetch('/api/user/menus', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })

    if (response.status === 401) {
      console.error('用户未登录或登录已过期')
      localStorage.removeItem('token')
      router.push('/login')
      return false
    }

    const data = await response.json()
    if (data.data) {
      const dynamicRoutes = generateRoutes(data.data)

      dynamicRoutes.forEach(route => {
        // 检查路由是否已经添加过
        if (!addedRouteNames.has(route.name)) {
          router.addRoute('Dashboard', route)
          addedRouteNames.add(route.name)
          console.log(`添加路由: ${route.path} (${route.name})`)
        } else {
          console.log(`路由已存在，跳过: ${route.path} (${route.name})`)
        }
      })

      console.log('动态路由添加完成')
      return true
    } else {
      console.error('获取菜单数据格式错误')
      return false
    }
  } catch (error) {
    console.error('获取菜单列表失败:', error)
    return false
  }
}

// 标记是否已加载动态路由
let dynamicRoutesLoaded = false

// 路由守卫
router.beforeEach(async (to, _from, next) => {
  const token = localStorage.getItem('token')

  // 如果访问需要认证的页面且没有token
  if (to.meta.requiresAuth && !token) {
    next({
      path: '/login',
      query: { redirect: to.fullPath } // 添加重定向参数
    })
    return
  }

  // 如果已登录但尝试访问登录页，重定向到首页
  if (to.path === '/login' && token) {
    next('/')
    return
  }

  // 如果用户已登录且动态路由未加载，则加载动态路由
  if (token && !dynamicRoutesLoaded && to.path !== '/login') {
    try {
      const success = await addDynamicRoutes()
      if (success) {
        dynamicRoutesLoaded = true
        // 只有当目标路由不存在时才重新导航
        if (!router.hasRoute(to.name)) {
          next({ ...to, replace: true })
          return
        }
      } else {
        // 如果加载动态路由失败，跳转到登录页
        next('/login')
        return
      }
    } catch (error) {
      console.error('加载动态路由失败:', error)
      next('/login')
      return
    }
  }

  next()
})

// 重置动态路由状态的函数（用于登出时调用）
export const resetDynamicRoutes = () => {
  dynamicRoutesLoaded = false
  addedRouteNames.clear() // 清空已添加的路由名称集合
  console.log('动态路由状态已重置')
}

export default router