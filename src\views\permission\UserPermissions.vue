<template>
  <div class="user-permissions">
    <!-- 搜索表单 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户ID">
          <el-input v-model="searchForm.user_id" placeholder="请输入用户ID" clearable />
        </el-form-item>
        <el-form-item label="用户名">
          <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable />
        </el-form-item>
        <el-form-item label="权限名称">
          <el-input v-model="searchForm.permission_name" placeholder="请输入权限名称" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="handleAdd">授予权限</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card" shadow="never">
      <el-table :data="tableData" border stripe>
        <el-table-column prop="user_id" label="用户ID" width="100" />
        <el-table-column prop="username" label="用户名" />
        <el-table-column label="权限名称">
          <template #default="{ row }">
            {{ row.permission?.name || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="权限描述">
          <template #default="{ row }">
            {{ row.permission?.description || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? '激活' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="granter_name" label="授权人" width="120" />
        <el-table-column prop="granted_at" label="授予时间" width="180" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="danger" size="small" @click="handleRevoke(row)">撤销</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 授予权限对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="授予权限"
      width="500px"
    >
      <el-form
        ref="userPermissionFormRef"
        :model="userPermissionForm"
        :rules="userPermissionFormRules"
        label-width="100px"
      >
        <el-form-item label="用户" prop="user_id">
          <el-select v-model="userPermissionForm.user_id" placeholder="请选择用户" filterable>
            <el-option
              v-for="user in userList"
              :key="user.id"
              :label="user.username"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="权限" prop="permission_id">
          <el-select v-model="userPermissionForm.permission_id" placeholder="请选择权限" filterable>
            <el-option
              v-for="permission in permissionList"
              :key="permission.id"
              :label="permission.name"
              :value="permission.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getUserPermissions,
  grantUserPermission,
  revokeUserPermission,
  getUsers,
  getPermissions
} from '../../api/permission'

// 搜索表单数据
const searchForm = ref({
  user_id: '',
  username: '',
  permission_name: ''
})

// 表格数据
const tableData = ref([])
const total = ref(0)

// 分页参数
const pagination = ref({
  pageNum: 1,
  pageSize: 10
})

// 对话框相关数据
const dialogVisible = ref(false)
const userPermissionFormRef = ref(null)
const userPermissionForm = ref({
  user_id: '',
  permission_id: ''
})

// 用户列表和权限列表
const userList = ref([])
const permissionList = ref([])

// 表单验证规则
const userPermissionFormRules = {
  user_id: [
    { required: true, message: '请选择用户', trigger: 'change' }
  ],
  permission_id: [
    { required: true, message: '请选择权限', trigger: 'change' }
  ]
}

// 获取用户权限列表数据
const getUserPermissionList = async () => {
  try {
    const params = {
      ...searchForm.value,
      ...pagination.value
    }
    const response = await getUserPermissions(params)
    if (response.data.code === 200) {
      tableData.value = response.data.data.list
      total.value = response.data.data.total
    } else {
      ElMessage.error(response.data.message || '获取用户权限列表失败')
    }
  } catch (error) {
    console.error('获取用户权限列表失败:', error)
    ElMessage.error('获取用户权限列表失败')
  }
}

// 获取用户列表
const getUserList = async () => {
  try {
    const response = await getUsers({ pageSize: 1000, pageNum: 1 })
    if (response.data.code === 200) {
      userList.value = response.data.data.list
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

// 获取权限列表
const getPermissionList = async () => {
  try {
    const response = await getPermissions({ pageSize: 1000, pageNum: 1 })
    if (response.data.code === 200) {
      permissionList.value = response.data.data.list
    }
  } catch (error) {
    console.error('获取权限列表失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.value.pageNum = 1
  getUserPermissionList()
}

// 重置
const handleReset = () => {
  searchForm.value = {
    user_id: '',
    username: '',
    permission_name: ''
  }
  pagination.value = {
    pageNum: 1,
    pageSize: 10
  }
  getUserPermissionList()
}

// 分页大小改变
const handleSizeChange = (val) => {
  pagination.value.pageSize = val
  getUserPermissionList()
}

// 页码改变
const handleCurrentChange = (val) => {
  pagination.value.pageNum = val
  getUserPermissionList()
}

// 打开授予权限对话框
const handleAdd = () => {
  userPermissionForm.value = {
    user_id: '',
    permission_id: ''
  }
  dialogVisible.value = true
}

// 撤销权限
const handleRevoke = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要撤销用户 "${row.username}" 的权限 "${row.permission?.name || '未知权限'}" 吗？`,
      '确认撤销',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await revokeUserPermission(row.id)
    if (response.data.code === 200) {
      ElMessage.success('撤销成功')
      getUserPermissionList()
    } else {
      ElMessage.error(response.data.message || '撤销失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤销权限失败:', error)
      ElMessage.error('撤销失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!userPermissionFormRef.value) return
  
  await userPermissionFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const response = await grantUserPermission(userPermissionForm.value)
        if (response.data.code === 200) {
          ElMessage.success('授予权限成功')
          dialogVisible.value = false
          getUserPermissionList()
        } else {
          ElMessage.error(response.data.message || '授予权限失败')
        }
      } catch (error) {
        console.error('授予权限失败:', error)
        ElMessage.error('授予权限失败')
      }
    }
  })
}

// 页面加载时获取数据
onMounted(() => {
  getUserPermissionList()
  getUserList()
  getPermissionList()
})
</script>

<style scoped>
.user-permissions {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}
</style>
