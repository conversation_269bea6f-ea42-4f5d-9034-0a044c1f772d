<template>
  <el-main class="app-main">
    <!-- 面包屑导航 -->
    <div class="breadcrumb-container" v-if="showBreadcrumb">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">
          <el-icon><House /></el-icon>
          <span>首页</span>
        </el-breadcrumb-item>
        <el-breadcrumb-item 
          v-for="item in breadcrumbList" 
          :key="item.path"
          :to="item.path ? { path: item.path } : null"
        >
          {{ item.title }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    
    <!-- 页面标题区域 -->
    <div class="page-header" v-if="pageTitle">
      <div class="page-title">
        <h2>{{ pageTitle }}</h2>
        <p v-if="pageDescription" class="page-description">{{ pageDescription }}</p>
      </div>
      <div class="page-actions" v-if="$slots.actions">
        <slot name="actions"></slot>
      </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="content-wrapper">
        <slot></slot>
      </div>
    </div>
  </el-main>
</template>

<script>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { House } from '@element-plus/icons-vue'

export default {
  name: 'AppMain',
  components: {
    House
  },
  props: {
    showBreadcrumb: {
      type: Boolean,
      default: true
    },
    pageTitle: {
      type: String,
      default: ''
    },
    pageDescription: {
      type: String,
      default: ''
    }
  },
  setup() {
    const route = useRoute()
    
    // 生成面包屑导航
    const breadcrumbList = computed(() => {
      const matched = route.matched.filter(item => item.meta && item.meta.title)
      const breadcrumbs = []
      
      matched.forEach(item => {
        if (item.meta.title && item.meta.title !== '首页') {
          breadcrumbs.push({
            title: item.meta.title,
            path: item.path === '/' ? null : item.path
          })
        }
      })
      
      return breadcrumbs
    })
    
    return {
      breadcrumbList
    }
  }
}
</script>

<style scoped>
.app-main {
  background-color: #f5f7fa;
  padding: 0;
  overflow-y: auto;
  height: 100%;
  flex: 1;
}

.breadcrumb-container {
  background: #fff;
  padding: 12px 24px;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 0;
}

.breadcrumb-container :deep(.el-breadcrumb__item) {
  display: flex;
  align-items: center;
}

.breadcrumb-container :deep(.el-breadcrumb__inner) {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #606266;
  font-weight: 400;
}

.breadcrumb-container :deep(.el-breadcrumb__inner:hover) {
  color: #409eff;
}

.breadcrumb-container :deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: #303133;
  font-weight: 500;
}

.page-header {
  background: #fff;
  padding: 24px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin: 20px 20px 20px 20px;
}

.page-title h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
}

.page-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.main-content {
  flex: 1;
  padding: 0 20px 20px 20px;
  overflow-y: auto;
}

.content-wrapper {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  min-height: calc(100vh - 200px);
  padding: 24px;
}

/* 当没有页面标题时，调整内容区域的上边距 */
.app-main:not(:has(.page-header)) .main-content {
  padding-top: 20px;
}

/* 当没有面包屑时，调整布局 */
.app-main:not(:has(.breadcrumb-container)) .page-header {
  margin-top: 20px;
}

.app-main:not(:has(.breadcrumb-container)):not(:has(.page-header)) .main-content {
  padding-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    margin: 16px;
    padding: 20px;
  }
  
  .page-title h2 {
    font-size: 20px;
  }
  
  .main-content {
    padding: 0 16px 16px 16px;
  }
  
  .content-wrapper {
    padding: 20px 16px;
    border-radius: 6px;
  }
  
  .breadcrumb-container {
    padding: 12px 16px;
  }
}

@media (max-width: 480px) {
  .page-header {
    margin: 12px;
    padding: 16px;
  }
  
  .page-title h2 {
    font-size: 18px;
  }
  
  .main-content {
    padding: 0 12px 12px 12px;
  }
  
  .content-wrapper {
    padding: 16px 12px;
    border-radius: 4px;
  }
  
  .breadcrumb-container {
    padding: 10px 12px;
  }
}

/* 滚动条样式 */
.app-main::-webkit-scrollbar {
  width: 6px;
}

.app-main::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.app-main::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.app-main::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
