kind: Deployment
apiVersion: apps/v1
metadata:
  name: iyunwei-vue
  namespace: uat
spec:
  replicas: 1
  selector:
    matchLabels:
      app: iyunwei-vue
  template:
    metadata:
      labels:
        app: iyunwei-vue
    spec:
      containers:
        - name: iyunwei-vue
          image: harbor.serviceshare.com/uat/iyunwei-vue:IMAGE_TAG
          ports:
            - containerPort: 30033
              protocol: TCP
          env:
            - name: TZ
              value: Asia/Shanghai
          resources:
            limits:
              memory: 1Gi
      restartPolicy: Always
      terminationGracePeriodSeconds: 120
      dnsPolicy: ClusterFirst
      securityContext: {}
      imagePullSecrets:
        - name: docker-regsitry-auth
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600