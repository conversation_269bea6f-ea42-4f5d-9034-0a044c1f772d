<template>
  <div class="log-alert-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span class="card-title">告警规则管理</span>
            </div>
          </template>
          <div class="alert-form">
            <el-form :inline="true" @submit.prevent="addRule">
              <el-form-item label="类型">
                <el-select v-model="newRule.type" placeholder="请选择类型" style="width: 120px;">
                  <el-option label="黑名单" value="black"></el-option>
                  <el-option label="白名单" value="white"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="值">
                <el-input v-model="newRule.value" placeholder="请输入值" style="width: 300px;"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="addRule">
                  <el-icon><Plus /></el-icon>添加
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <div class="alert-list">
            <el-table :data="rules" style="width: 100%">
              <el-table-column prop="id" label="ID" width="80">
                <template #default="scope">
                  <el-tag size="small" effect="plain">{{ scope.row.id }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="type" label="类型" width="120">
                <template #default="scope">
                  <el-tag :type="scope.row.type === 'black' ? 'danger' : 'info'">
                    {{ scope.row.type === 'black' ? '黑名单' : '白名单' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="value" label="值"></el-table-column>
              <el-table-column prop="created_at" label="创建日期" width="180">
                <template #default="scope">
                  {{ scope.row.created_at ? new Date(scope.row.created_at).toLocaleString() : '未知' }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center">
                <template #default="scope">
                  <el-button type="danger" size="small" @click="deleteRule(scope.row.id)">
                    <el-icon><Delete /></el-icon>删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'

export default {
  name: 'LogAlert',
  components: {
    Plus,
    Delete
  },
  setup() {
    const rules = ref([])
    const newRule = ref({
      type: '',
      value: ''
    })

    const addRule = async () => {
      if (!newRule.value.type || !newRule.value.value) {
        ElMessage.warning('请填写完整信息')
        return
      }

      try {
        const apiUrl = `${process.env.VUE_APP_API_URL || '/api'}/logalert/add`
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify(newRule.value)
        })
        const data = await response.json()
        if (data.success) {
          await loadRules()
          ElMessage.success(data.message)
          newRule.value.type = ''
          newRule.value.value = ''
        }
      } catch (error) {
        ElMessage.error('添加规则失败')
        console.error(error)
      }
    }

    const deleteRule = async (id) => {
      try {
        await ElMessageBox.confirm('确定要删除此规则吗？', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const apiUrl = `${process.env.VUE_APP_API_URL || '/api'}/logalert/delete/${id}`
        const response = await fetch(apiUrl, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })
        const data = await response.json()
        if (data.success) {
          await loadRules()
          ElMessage.success(data.message)
        }
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除规则失败')
          console.error(error)
        }
      }
    }

    const loadRules = async () => {
      try {
        const response = await fetch('/api/logalert', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })
        const data = await response.json()
        if (data.success) {
          rules.value = data.data
        }
      } catch (error) {
        ElMessage.error('获取规则列表失败')
        console.error(error)
      }
    }

    onMounted(async () => {
      await loadRules()
    })

    return {
      rules,
      newRule,
      addRule,
      deleteRule
    }
  }
}
</script>

<style scoped>
.log-alert-container {
  padding: 20px;
}



.alert-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.alert-list {
  margin-top: 20px;
}
</style>