import axios from 'axios'
import { ElMessage } from 'element-plus'
import store from '../store'
import router from '../router'

const api = axios.create({
  baseURL: process.env.VUE_APP_API_URL || '/api',
  timeout: 35000
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    const requestPath = config.url.split('?')[0]
    const authRequiredPaths = ['/api/fail2ban', '/api/logalert', '/api/user', '/api/links', '/api/redis', '/api/wiki', '/api/permission', '/api/alertmanager']
    
    // 检查当前请求是否需要认证（精确匹配路径或路径前缀）
    const needsAuth = authRequiredPaths.some(path => 
      requestPath === path || 
      requestPath.startsWith(`${path}/`)
    )
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    } else if (needsAuth) {
      // 如果请求需要认证但没有token，取消请求并跳转到登录页
      console.log('需要登录权限，正在跳转到登录页', requestPath)
      store.dispatch('logout')
      router.push('/login')
      return Promise.reject(new Error('未登录，请先登录'))
    }
    
    return config
  },
  error => {
    console.error('Request interceptor error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      store.dispatch('logout')
      router.push('/login')
    }
    ElMessage.error(error.response?.data?.message || '请求失败')
    return Promise.reject(error)
  }
)

export default api