import { createStore } from 'vuex'
import axios from 'axios'
import { resetDynamicRoutes } from '../router'

// Create store first
const store = createStore({
  state: {
    user: null,
    token: localStorage.getItem('token')
  },
  getters: {
  },
  mutations: {
    SET_USER(state, user) {
      state.user = user
    },
    SET_TOKEN(state, token) {
      state.token = token
      localStorage.setItem('token', token)
    },
    CLEAR_AUTH(state) {
      state.user = null
      state.token = null
      localStorage.removeItem('token')
      resetDynamicRoutes() // 重置动态路由状态
    }
  },
  actions: {
    async login({ commit }, credentials) {
      try {
        const response = await axios.post('/api/login', credentials)
        commit('SET_TOKEN', response.data.token)
        commit('SET_USER', response.data.user)
      } catch (error) {
        throw new Error(error.response?.data?.message || '登录失败')
      }
    },
    async checkAuth({ commit, state }) {
      if (!state.token) return
      try {
        const response = await axios.get('/api/user/info')
        commit('SET_USER', response.data.data)
      } catch (error) {
        commit('CLEAR_AUTH')
      }
    },
    logout({ commit }) {
      commit('CLEAR_AUTH')
    }
  },
  modules: {
  }
})

// Then setup axios interceptor using the store instance
axios.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      // token 过期，清除登录状态
      store.commit('CLEAR_AUTH')
      // 可以选择重定向到登录页
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

export default store