import api from './index'

// 权限管理接口

/**
 * 获取权限列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageSize - 每页数量
 * @param {number} params.pageNum - 页码
 * @param {string} params.module - 模块名称
 * @param {string} params.function_name - 功能名称
 */
export const getPermissions = (params) => {
  return api.get('/permission/permissions', { params })
}

/**
 * 创建权限
 * @param {Object} data - 权限数据
 * @param {string} data.name - 权限名称
 * @param {string} data.description - 权限描述
 * @param {string} data.module - 模块名称
 * @param {string} data.function_name - 功能名称
 */
export const createPermission = (data) => {
  return api.post('/permission/permissions', data)
}

/**
 * 更新权限
 * @param {number} permissionId - 权限ID
 * @param {Object} data - 权限数据
 */
export const updatePermission = (permissionId, data) => {
  return api.put(`/permission/permissions/${permissionId}`, data)
}

/**
 * 删除权限
 * @param {number} permissionId - 权限ID
 */
export const deletePermission = (permissionId) => {
  return api.delete(`/permission/permissions/${permissionId}`)
}

// 用户权限管理接口

/**
 * 获取用户权限列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageSize - 每页数量
 * @param {number} params.pageNum - 页码
 * @param {string} params.user_id - 用户ID
 * @param {string} params.permission_name - 权限名称
 */
export const getUserPermissions = (params) => {
  return api.get('/permission/user-permissions', { params })
}

/**
 * 授予用户权限
 * @param {Object} data - 用户权限数据
 * @param {number} data.user_id - 用户ID
 * @param {number} data.permission_id - 权限ID
 */
export const grantUserPermission = (data) => {
  return api.post('/permission/user-permissions', data)
}

/**
 * 撤销用户权限
 * @param {number} userPermissionId - 用户权限ID
 */
export const revokeUserPermission = (userPermissionId) => {
  return api.delete(`/permission/user-permissions/${userPermissionId}`)
}

// 权限检查接口

/**
 * 检查用户权限
 * @param {Object} data - 权限检查数据
 * @param {string} data.permission_name - 权限名称
 */
export const checkPermission = (data) => {
  return api.post('/permission/check-permission', data)
}

// 用户管理接口

/**
 * 获取用户列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageSize - 每页数量
 * @param {number} params.pageNum - 页码
 * @param {string} params.username - 用户名
 * @param {string} params.phone - 手机号
 */
export const getUsers = (params) => {
  return api.get('/permission-management/users', { params })
}

/**
 * 创建用户
 * @param {Object} data - 用户数据
 * @param {string} data.username - 用户名
 * @param {string} data.phone - 手机号
 * @param {string} data.email - 邮箱
 * @param {string} data.password - 密码
 * @param {string} data.role - 角色
 */
export const createUser = (data) => {
  return api.post('/permission-management/users', data)
}

/**
 * 更新用户
 * @param {number} userId - 用户ID
 * @param {Object} data - 用户数据
 */
export const updateUser = (userId, data) => {
  return api.put(`/permission-management/users/${userId}`, data)
}

/**
 * 删除用户
 * @param {number} userId - 用户ID
 */
export const deleteUser = (userId) => {
  return api.delete(`/permission-management/users/${userId}`)
}

// 日志查询接口

/**
 * 获取操作日志（管理员）
 * @param {Object} params - 查询参数
 * @param {number} params.pageSize - 每页数量
 * @param {number} params.pageNum - 页码
 * @param {string} params.user_id - 用户ID
 * @param {string} params.module - 模块名称
 * @param {string} params.function_name - 功能名称
 * @param {string} params.start_date - 开始日期
 * @param {string} params.end_date - 结束日期
 */
export const getOperationLogs = (params) => {
  return api.get('/permission/operation-logs', { params })
}
