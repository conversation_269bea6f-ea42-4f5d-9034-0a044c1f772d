import api from './index'

// ==================== 告警查询接口 ====================

/**
 * 获取告警列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，默认1
 * @param {number} params.per_page - 每页数量，默认20
 * @param {string} params.status - 状态过滤: active, resolved
 * @param {string} params.alert_level - 告警等级过滤
 * @param {string} params.project_category - 项目分类过滤
 * @param {string} params.project_owner - 负责人过滤
 * @param {string} params.start_date - 开始日期: YYYY-MM-DD
 * @param {string} params.end_date - 结束日期: YYYY-MM-DD
 */
export const getAlertLogs = (params = {}) => {
  return api.get('/alertmanager/logs', { params })
}

/**
 * 获取告警详情
 * @param {number} id - 告警ID
 */
export const getAlertDetail = (id) => {
  return api.get(`/alertmanager/logs/${id}`)
}

/**
 * 获取告警统计
 */
export const getAlertStats = () => {
  return api.get('/alertmanager/logs/stats')
}

// ==================== 告警管理接口 ====================

/**
 * 标记单个告警解决
 * @param {number} id - 告警ID
 */
export const resolveAlert = (id) => {
  return api.put(`/alertmanager/logs/${id}/resolve`)
}

/**
 * 批量标记告警解决
 * @param {Object} data - 请求数据
 * @param {Array<number>} data.alert_ids - 告警ID数组
 */
export const batchResolveAlerts = (data) => {
  return api.put('/alertmanager/logs/batch-resolve', data)
}

// ==================== Webhook配置管理接口 ====================

/**
 * 获取Webhook配置列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，默认1
 * @param {number} params.per_page - 每页数量，默认20
 */
export const getWebhookConfigs = (params = {}) => {
  return api.get('/alertmanager/config/webhooks', { params })
}

/**
 * 创建Webhook配置
 * @param {Object} data - 配置数据
 * @param {string} data.project_category - 项目分类
 * @param {string} data.ip_pattern - IP模式
 * @param {string} data.datacenter - 数据中心
 * @param {string} data.environment - 环境
 * @param {string} data.webhook_url - Webhook URL
 * @param {string} data.group_name - 群组名称
 * @param {boolean} data.is_active - 是否启用
 */
export const createWebhookConfig = (data) => {
  return api.post('/alertmanager/config/webhooks', data)
}

/**
 * 更新Webhook配置
 * @param {number} id - 配置ID
 * @param {Object} data - 配置数据
 */
export const updateWebhookConfig = (id, data) => {
  return api.put(`/alertmanager/config/webhooks/${id}`, data)
}

/**
 * 删除Webhook配置
 * @param {number} id - 配置ID
 */
export const deleteWebhookConfig = (id) => {
  return api.delete(`/alertmanager/config/webhooks/${id}`)
}

/**
 * 获取Webhook配置详情
 * @param {number} id - 配置ID
 */
export const getWebhookConfigDetail = (id) => {
  return api.get(`/alertmanager/config/webhooks/${id}`)
}

// ==================== 静默规则管理接口 ====================

/**
 * 获取静默规则列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，默认1
 * @param {number} params.per_page - 每页数量，默认20
 */
export const getSilenceRules = (params = {}) => {
  return api.get('/alertmanager/config/silence-rules', { params })
}

/**
 * 创建静默规则
 * @param {Object} data - 规则数据
 * @param {string} data.rule_name - 规则名称
 * @param {string} data.alert_title_pattern - 告警标题模式
 * @param {string} data.project_category - 项目分类
 * @param {string} data.alert_level - 告警等级
 * @param {string} data.ip_pattern - IP模式
 * @param {number} data.silence_hours - 静默小时数
 * @param {string} data.reason - 静默原因
 */
export const createSilenceRule = (data) => {
  return api.post('/alertmanager/config/silence-rules', data)
}

/**
 * 更新静默规则
 * @param {number} id - 规则ID
 * @param {Object} data - 规则数据
 */
export const updateSilenceRule = (id, data) => {
  return api.put(`/alertmanager/config/silence-rules/${id}`, data)
}

/**
 * 删除静默规则
 * @param {number} id - 规则ID
 */
export const deleteSilenceRule = (id) => {
  return api.delete(`/alertmanager/config/silence-rules/${id}`)
}

/**
 * 获取静默规则详情
 * @param {number} id - 规则ID
 */
export const getSilenceRuleDetail = (id) => {
  return api.get(`/alertmanager/config/silence-rules/${id}`)
}

/**
 * 启用/禁用静默规则
 * @param {number} id - 规则ID
 * @param {boolean} isActive - 是否启用
 */
export const toggleSilenceRule = (id, isActive) => {
  return api.put(`/alertmanager/config/silence-rules/${id}/toggle`, { is_active: isActive })
}

// ==================== 告警接收接口（用于测试） ====================

/**
 * 接收告警数据（Webhook接口）
 * @param {Object} data - 告警数据
 */
export const receiveAlerts = (data) => {
  return api.post('/v2/alerts', data)
}
