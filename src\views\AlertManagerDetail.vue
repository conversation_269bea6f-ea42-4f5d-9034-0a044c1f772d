<template>
  <div class="alert-detail-container">
    <!-- 返回按钮 -->
    <div class="back-button-container">
      <el-button @click="goBack" :icon="ArrowLeft">
        返回告警列表
      </el-button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <!-- 告警详情 -->
    <div v-else-if="alertDetail" class="alert-detail-content">
      <!-- 基本信息卡片 -->
      <el-card class="basic-info-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">告警基本信息</span>
            <div class="header-actions">
              <el-tag
                :type="getStatusType(alertDetail.status)"
                size="large"
                class="status-tag"
              >
                <el-icon class="status-icon">
                  <component :is="getStatusIcon(alertDetail.status)" />
                </el-icon>
                {{ getStatusText(alertDetail.status) }}
              </el-tag>
              <el-tag
                :type="getLevelType(alertDetail.alert_level)"
                size="large"
                class="level-tag"
              >
                {{ getLevelText(alertDetail.alert_level) }}
              </el-tag>
              <el-button
                v-if="alertDetail.status === 'active'"
                type="success"
                @click="resolveAlert"
                :loading="resolving"
                :icon="Check"
              >
                标记为已解决
              </el-button>
            </div>
          </div>
        </template>

        <div class="basic-info-content">
          <el-row :gutter="24">
            <el-col :span="12">
              <div class="info-group">
                <h4>告警信息</h4>
                <div class="info-item">
                  <span class="label">告警ID:</span>
                  <span class="value">#{{ alertDetail.id }}</span>
                </div>
                <div class="info-item">
                  <span class="label">告警标题:</span>
                  <span class="value">{{ alertDetail.alert_title }}</span>
                </div>
                <div class="info-item">
                  <span class="label">告警描述:</span>
                  <span class="value">{{ alertDetail.alert_description }}</span>
                </div>
                <div class="info-item">
                  <span class="label">告警等级:</span>
                  <span class="value">
                    <el-tag :type="getLevelType(alertDetail.alert_level)" size="small">
                      {{ getLevelText(alertDetail.alert_level) }}
                    </el-tag>
                  </span>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-group">
                <h4>项目信息</h4>
                <div class="info-item">
                  <span class="label">项目分类:</span>
                  <span class="value">{{ alertDetail.project_category }}</span>
                </div>
                <div class="info-item">
                  <span class="label">负责人:</span>
                  <span class="value">{{ alertDetail.project_owner }}</span>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="24" style="margin-top: 24px;">
            <el-col :span="12">
              <div class="info-group">
                <h4>时间信息</h4>
                <div class="info-item">
                  <span class="label">告警时间:</span>
                  <span class="value">{{ alertDetail.alert_time }}</span>
                </div>
                <div class="info-item" v-if="alertDetail.recovery_time">
                  <span class="label">恢复时间:</span>
                  <span class="value">{{ alertDetail.recovery_time }}</span>
                </div>
                <div class="info-item">
                  <span class="label">创建时间:</span>
                  <span class="value">{{ alertDetail.created_at }}</span>
                </div>
                <div class="info-item">
                  <span class="label">更新时间:</span>
                  <span class="value">{{ alertDetail.updated_at }}</span>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-group">
                <h4>状态信息</h4>
                <div class="info-item">
                  <span class="label">当前状态:</span>
                  <span class="value">
                    <el-tag :type="getStatusType(alertDetail.status)" size="small">
                      {{ getStatusText(alertDetail.status) }}
                    </el-tag>
                  </span>
                </div>
                <div class="info-item" v-if="alertDetail.status === 'resolved'">
                  <span class="label">持续时间:</span>
                  <span class="value">{{ getDuration() }}</span>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 原始数据卡片 -->
      <el-card class="raw-data-card" v-if="alertDetail.raw_data">
        <template #header>
          <div class="card-header">
            <span class="card-title">原始告警数据</span>
            <el-button @click="copyRawData" :icon="CopyDocument" size="small">
              复制数据
            </el-button>
          </div>
        </template>

        <div class="raw-data-content">
          <el-tabs v-model="activeTab" type="border-card">
            <el-tab-pane label="格式化显示" name="formatted">
              <div class="formatted-data">
                <div v-if="alertDetail.raw_data.annotations" class="data-section">
                  <h5>Annotations</h5>
                  <div class="data-item" v-for="(value, key) in alertDetail.raw_data.annotations" :key="key">
                    <span class="data-key">{{ key }}:</span>
                    <span class="data-value">{{ value }}</span>
                  </div>
                </div>
                
                <div v-if="alertDetail.raw_data.labels" class="data-section">
                  <h5>Labels</h5>
                  <div class="data-item" v-for="(value, key) in alertDetail.raw_data.labels" :key="key">
                    <span class="data-key">{{ key }}:</span>
                    <span class="data-value">{{ value }}</span>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="JSON格式" name="json">
              <div class="json-data">
                <pre><code>{{ formatJSON(alertDetail.raw_data) }}</code></pre>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-card>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <el-result
        icon="error"
        title="加载失败"
        sub-title="无法获取告警详情，请检查告警ID是否正确"
      >
        <template #extra>
          <el-button type="primary" @click="loadAlertDetail">重试</el-button>
          <el-button @click="goBack">返回列表</el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Check,
  CopyDocument,
  CircleCheck,
  Clock,
  Bell,
  Warning
} from '@element-plus/icons-vue'
import { getAlertDetail, resolveAlert as resolveAlertApi } from '../api/alertmanager'

export default {
  name: 'AlertManagerDetail',
  components: {
    ArrowLeft,
    Check,
    CopyDocument,
    CircleCheck,
    Clock,
    Bell,
    Warning
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    
    const loading = ref(false)
    const resolving = ref(false)
    const alertDetail = ref(null)
    const activeTab = ref('formatted')
    
    const alertId = computed(() => route.params.id)

    // 加载告警详情
    const loadAlertDetail = async () => {
      loading.value = true
      try {
        const response = await getAlertDetail(alertId.value)
        
        if (response.data.success) {
          alertDetail.value = response.data.data
        } else {
          ElMessage.error(response.data.message || '获取告警详情失败')
        }
      } catch (error) {
        ElMessage.error('获取告警详情失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loading.value = false
      }
    }

    // 返回列表
    const goBack = () => {
      router.push('/alertmanager/list')
    }

    // 解决告警
    const resolveAlert = async () => {
      try {
        await ElMessageBox.confirm(
          `确定要标记告警 #${alertDetail.value.id} 为已解决吗？`,
          '确认解决',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        resolving.value = true

        const response = await resolveAlertApi(alertDetail.value.id)

        if (response.data.success) {
          ElMessage.success(response.data.message || '告警已标记为解决')
          // 重新加载详情
          loadAlertDetail()
        } else {
          ElMessage.error(response.data.message || '操作失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('操作失败: ' + (error.response?.data?.message || error.message))
        }
      } finally {
        resolving.value = false
      }
    }

    // 复制原始数据
    const copyRawData = async () => {
      try {
        const text = JSON.stringify(alertDetail.value.raw_data, null, 2)
        await navigator.clipboard.writeText(text)
        ElMessage.success('原始数据已复制到剪贴板')
      } catch (error) {
        ElMessage.error('复制失败')
      }
    }

    // 格式化JSON
    const formatJSON = (data) => {
      return JSON.stringify(data, null, 2)
    }

    // 计算持续时间
    const getDuration = () => {
      if (!alertDetail.value.alert_time || !alertDetail.value.recovery_time) {
        return '未知'
      }

      const start = new Date(alertDetail.value.alert_time)
      const end = new Date(alertDetail.value.recovery_time)
      const diff = end - start

      const hours = Math.floor(diff / (1000 * 60 * 60))
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

      if (hours > 0) {
        return `${hours}小时${minutes}分钟`
      } else {
        return `${minutes}分钟`
      }
    }

    // 工具函数
    const getStatusType = (status) => {
      switch (status) {
        case 'active':
          return 'danger'
        case 'resolved':
          return 'success'
        default:
          return 'info'
      }
    }

    const getStatusIcon = (status) => {
      switch (status) {
        case 'active':
          return Bell
        case 'resolved':
          return CircleCheck
        default:
          return Warning
      }
    }

    const getStatusText = (status) => {
      switch (status) {
        case 'active':
          return '活跃'
        case 'resolved':
          return '已解决'
        default:
          return '未知'
      }
    }

    const getLevelType = (level) => {
      switch (level) {
        case 'critical':
          return 'danger'
        case 'warning':
          return 'warning'
        case 'info':
          return 'info'
        default:
          return 'info'
      }
    }

    const getLevelText = (level) => {
      switch (level) {
        case 'critical':
          return '严重'
        case 'warning':
          return '警告'
        case 'info':
          return '信息'
        default:
          return '未知'
      }
    }

    // 初始化
    onMounted(() => {
      loadAlertDetail()
    })

    return {
      loading,
      resolving,
      alertDetail,
      activeTab,
      alertId,
      loadAlertDetail,
      goBack,
      resolveAlert,
      copyRawData,
      formatJSON,
      getDuration,
      getStatusType,
      getStatusIcon,
      getStatusText,
      getLevelType,
      getLevelText
    }
  }
}
</script>

<style scoped>
.alert-detail-container {
  padding: var(--spacing-lg);
  background: var(--bg-page);
  min-height: calc(100vh - 60px);
}

.back-button-container {
  margin-bottom: var(--spacing-lg);
}

.loading-container {
  padding: calc(var(--spacing-xl) + var(--spacing-sm));
}

.error-container {
  padding: calc(var(--spacing-xl) + var(--spacing-sm));
  text-align: center;
}

.alert-detail-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.basic-info-card, .raw-data-card {
  margin-bottom: var(--spacing-lg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.status-tag, .level-tag {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.status-icon {
  font-size: var(--font-size-sm);
}

.basic-info-content {
  padding: var(--spacing-md) 0;
}

.info-group {
  margin-bottom: var(--spacing-lg);
}

.info-group h4 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-lighter);
  padding-bottom: var(--spacing-xs);
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: var(--spacing-sm);
  min-height: 24px;
}

.info-item .label {
  color: var(--text-secondary);
  min-width: 100px;
  flex-shrink: 0;
  font-weight: 500;
}

.info-item .value {
  color: var(--text-primary);
  flex: 1;
  word-break: break-all;
}

.raw-data-content {
  padding: var(--spacing-sm) 0;
}

.formatted-data {
  padding: var(--spacing-md);
}

.data-section {
  margin-bottom: var(--spacing-lg);
}

.data-section h5 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--primary-color);
  background: var(--bg-page);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-base);
}

.data-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: var(--spacing-xs);
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid var(--border-extra-light);
}

.data-key {
  color: var(--text-secondary);
  min-width: 150px;
  flex-shrink: 0;
  font-weight: 500;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.data-value {
  color: var(--text-primary);
  flex: 1;
  word-break: break-all;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: var(--bg-page);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-small);
}

.json-data {
  background: var(--bg-page);
  border-radius: var(--border-radius-base);
  padding: var(--spacing-md);
  overflow-x: auto;
}

.json-data pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: var(--font-size-xs);
  line-height: 1.5;
  color: var(--text-primary);
}

.json-data code {
  background: none;
  padding: 0;
  border-radius: 0;
  font-size: inherit;
  color: inherit;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .alert-detail-container {
    padding: var(--spacing-sm);
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .info-item .label {
    min-width: auto;
    font-weight: 600;
  }

  .data-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .data-key {
    min-width: auto;
    font-weight: 600;
  }

  .data-value {
    width: 100%;
  }

  .json-data {
    font-size: var(--font-size-xs);
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .header-actions .el-button {
    width: 100%;
  }
}
</style>
