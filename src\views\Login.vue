<template>
  <div class="login-container">
    <div class="login-box">
      <h1 class="title">系统登录</h1>
      <el-form :model="loginForm" :rules="rules" ref="loginFormRef">
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" prefix-icon="el-icon-user" placeholder="用户名"></el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input v-model="loginForm.password" prefix-icon="el-icon-lock" type="password" placeholder="密码" @keyup.enter="handleLogin"></el-input>
        </el-form-item>
          <el-form-item prop="captcha">
          <div class="captcha-container">
            <el-input v-model="loginForm.captcha" prefix-icon="el-icon-key" placeholder="验证码"></el-input>
            <div class="captcha-wrapper">
              <img :src="captchaImage"  alt="验证码" class="captcha-img" @click="refreshCaptcha" v-if="captchaImage">
              <div v-else class="captcha-loading">加载中...</div>
            </div>
          </div>
          </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="loading" class="login-btn" @click="handleLogin">登录</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import axios from 'axios'

export default {
  name: 'Login',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const loginFormRef = ref(null)
    const loading = ref(false)
    const captchaUrl = ref('')
    const captchaImage = ref('')
    const captchaToken = ref('')
    
    const loginForm = reactive({
      username: '',
      password: '',
      captcha: ''
    })
    
    const rules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' }
      ],
      captcha: [
        { required: true, message: '请输入验证码', trigger: 'blur' }
      ]
    }
    
    // 刷新验证码
    const refreshCaptcha = async () => {
      try {
        const res = await axios.get('/api/captcha')
        if (res.data.code === 0) {
          captchaImage.value = res.data.data.image
          captchaToken.value = res.data.data.captcha_token
        } else {
          ElMessage.error('验证码获取失败: ' + (res.data.message || '未知错误'))
        }
      } catch (error) {
        ElMessage.error('验证码加载失败: ' + (error.response?.data?.message || error.message))
        captchaImage.value = ''
      }
    }
    
    // 页面加载时获取验证码
    onMounted(() => {
      refreshCaptcha()
    })
    
    const handleLogin = () => {
      loginFormRef.value.validate(async (valid) => {
        if (!valid) return
        
        loading.value = true
        try {
          const response = await axios.post('/api/login', {
            username: loginForm.username,
            password: loginForm.password,
            captcha: loginForm.captcha.toUpperCase(),
            captcha_token: captchaToken.value
          })

          if (response.data.code === 0) {
            localStorage.setItem('token', response.data.data.token)
            ElMessage.success('登录成功')
            const redirectPath = route.query.redirect || '/'
            router.push(redirectPath)
          } else {
            ElMessage.error(response.data.message || '登录失败')
            refreshCaptcha()  // 失败后刷新验证码
          }
        } catch (error) {
          const errMsg = error.response?.data?.message || error.message
          if (error.response?.status === 401) {
            refreshCaptcha()  // 认证错误时刷新验证码
          }
          if (error.response?.data?.code === 1) {
            loginForm.captcha = ''  // 清空验证码输入
          }
          ElMessage.error(`登录失败: ${errMsg}`)
        } finally {
          loading.value = false
        }
      })
    }
    
    return {
      loginForm,
      loginFormRef,
      rules,
      loading,
      handleLogin,
      captchaUrl,
      captchaImage,
      refreshCaptcha
    }
  }
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
}

.login-box {
  width: 400px;
  padding: 30px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.title {
  text-align: center;
  margin-bottom: 30px;
  color: #303133;
}

.login-btn {
  width: 100%;
}

.captcha-wrapper {
  width: 120px;
  margin-left: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.captcha-loading {
  color: #909399;
  font-size: 14px;
}
.captcha-container {
  display: flex;
  align-items: center;
}

.captcha-img {
  margin-left: 10px;
  height: 40px;
  cursor: pointer;
}
</style> 