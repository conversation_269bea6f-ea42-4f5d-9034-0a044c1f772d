<template>
  <div class="blue-green-deploy">
    
    <div class="env-selector">
      <div class="env-radio-group">
        <el-radio-group v-model="currentEnv" @change="fetchNacosStatus">
          <el-radio-button label="lty">联通云</el-radio-button>
          <el-radio-button label="prod">亦庄</el-radio-button>
          <el-radio-button label="aliyun">易畅行</el-radio-button>
          <el-radio-button label="sby">新亦庄</el-radio-button>
        </el-radio-group>
      </div>

      <div class="action-buttons">
        <el-button
          :type="showUnknownVersions ? 'warning' : 'info'"
          @click="toggleUnknownVersions"
          class="toggle-btn"
        >
          <el-icon><View v-if="!showUnknownVersions" /><Hide v-if="showUnknownVersions" /></el-icon>
          {{ showUnknownVersions ? '隐藏未知版本' : '显示未知版本' }}
        </el-button>
        <el-button type="primary" @click="fetchNacosStatus" :loading="loading" class="refresh-btn">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
    
    <div v-if="loading" class="loading-container">
      <el-spinner></el-spinner>
      <p>加载中...</p>
    </div>
    
    <div v-else class="table-container">
      <div class="table-header">
        <h2>服务详细信息（按Git版本分组）</h2>
        <div class="table-stats">
          <el-tag type="info">共 {{ filteredGroupedServerInfoData.length }} 个版本</el-tag>
          <el-tag v-if="!showUnknownVersions && unknownVersionCount > 0" type="warning">
            已隐藏 {{ unknownVersionCount }} 个未知版本服务
          </el-tag>
        </div>
      </div>

      <el-table
        :data="filteredGroupedServerInfoData"
        border
        stripe
        class="main-table"
        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266', fontWeight: 'bold' }"
      >
        <el-table-column type="expand">
          <template #default="props">
            <div class="expand-content">
              <h4>{{ props.row.gitVersion }} 版本详细信息</h4>
              <el-table
                :data="props.row.services"
                border
                size="small"
                class="detail-table"
              >
                <el-table-column prop="server_name" label="服务名称" width="250">
                  <template #default="scope">
                    <el-tag type="primary" size="small">{{ scope.row.server_name }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="ip" label="IP地址" width="150">
                  <template #default="scope">
                    <code class="ip-code">{{ scope.row.ip }}</code>
                  </template>
                </el-table-column>
                <el-table-column prop="build_version" label="Git版本" width="200">
                  <template #default="scope">
                    <el-tag type="success" size="small">{{ scope.row.build_version }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="build_date" label="合并时间">
                  <template #default="scope">
                    <span class="build-date">{{ scope.row.build_date }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="gitVersion" label="Git版本" width="280">
          <template #default="scope">
            <div class="git-version-cell">
              <el-tag type="success" size="large">{{ scope.row.gitVersion }}</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="count" label="服务数量" width="120" align="center">
          <template #default="scope">
            <div class="count-display">
              <span class="count-number">{{ scope.row.count }}</span>
              <span class="count-unit">个</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="服务列表">
          <template #default="scope">
            <div class="service-tags">
              <el-tag
                v-for="(service, index) in scope.row.serviceNames"
                :key="index"
                type="info"
                size="small"
                class="service-tag"
              >
                {{ service }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { Refresh, View, Hide } from '@element-plus/icons-vue'

export default {
  name: 'BlueGreenDeploy',
  components: {
    Refresh,
    View,
    Hide
  },
  setup() {
    const currentEnv = ref('lty')
    const loading = ref(false)
    const serverInfo = ref([])
    const showUnknownVersions = ref(false) // 默认不显示未知版本
    
    // 根据环境获取对应的域名
    const getDomainByEnv = (env) => {
      switch(env) {
        case 'lty':
          return 'https://ltynacos.serviceshare.com'
        case 'prod':
          return 'https://nacos.serviceshare.com'
        case 'aliyun':
          return 'https://nacos.ycxlg.com'
        case 'sby':
          return 'https://bingenacos.serviceshare.com'
        default:
          return 'https://ltynacos.serviceshare.com'
      }
    }
    
    // 获取服务列表
    const getServers = async (domain) => {
      try {
        const response = await axios.get(`${domain}/nacos/v1/ns/service/list`, {
          params: {
            pageNo: 0,
            pageSize: 100,
            namespaceId: "prod"
          }
        })
        
        if (response.status === 200 && response.data) {
          return response.data.doms || []
        }
        return []
      } catch (error) {
        console.error('获取服务列表失败:', error)
        return []
      }
    }
    
    // 获取服务详情
    const getServerInfo = async (domain, serverName) => {
      try {
        const response = await axios.get(`${domain}/nacos/v1/ns/instance/list`, {
          params: {
            serviceName: serverName,
            namespaceId: "prod"
          }
        })
        
        if (response.status === 200 && response.data) {
          const hosts = response.data.hosts || []
          return hosts.map(host => {
            const buildVersionParts = (host.metadata["build-version"] || '||').split('|', 3)
            return {
              ip: host.ip,
              server_name: host.serviceName,
              build_version: buildVersionParts[0] || '未知',
              build_date: buildVersionParts[1] || '未知',
              git_version: buildVersionParts[0] || '未知' // 修改这里，使用构建版本作为git版本
            }
          })
        }
        return []
      } catch (error) {
        console.error(`获取服务 ${serverName} 详情失败:`, error)
        return []
      }
    }
    
    // 按Git版本分组的服务详细信息
    const groupedServerInfoData = computed(() => {
      const groups = {}

      // 按Git版本分组
      serverInfo.value.forEach(item => {
        const gitVersion = item.git_version
        if (!groups[gitVersion]) {
          groups[gitVersion] = {
            gitVersion,
            services: [],
            serviceNames: new Set(),
            count: 0
          }
        }
        groups[gitVersion].services.push(item)
        groups[gitVersion].serviceNames.add(item.server_name)
      })

      // 转换为数组并计算服务数量
      return Object.values(groups).map(group => {
        group.serviceNames = Array.from(group.serviceNames)
        group.count = group.serviceNames.length
        // 按服务名称排序服务列表
        group.services.sort((a, b) => a.server_name.localeCompare(b.server_name))
        return group
      }).sort((a, b) => {
        // 按服务数量降序排序
        return b.count - a.count
      })
    })

    // 过滤后的分组数据（根据是否显示未知版本）
    const filteredGroupedServerInfoData = computed(() => {
      if (showUnknownVersions.value) {
        return groupedServerInfoData.value
      }
      return groupedServerInfoData.value.filter(group => group.gitVersion !== '未知')
    })

    // 未知版本服务数量
    const unknownVersionCount = computed(() => {
      const unknownGroup = groupedServerInfoData.value.find(group => group.gitVersion === '未知')
      return unknownGroup ? unknownGroup.count : 0
    })

    // 切换显示/隐藏未知版本
    const toggleUnknownVersions = () => {
      showUnknownVersions.value = !showUnknownVersions.value
    }
    
    const fetchNacosStatus = async () => {
      loading.value = true
      serverInfo.value = []
      
      try {
        const domain = getDomainByEnv(currentEnv.value)
        const servers = await getServers(domain)
        
        // 遍历每个服务获取详情
        for (const server of servers) {
          if (server !== 'sentinel-dashboard') {
            const details = await getServerInfo(domain, server)
            
            // 添加到服务信息列表
            serverInfo.value.push(...details)
          }
        }
      } catch (error) {
        console.error('获取Nacos状态失败:', error)
        ElMessage.error(`获取Nacos状态失败: ${error.message || '未知错误'}`)
      } finally {
        loading.value = false
      }
    }
    
    onMounted(() => {
      fetchNacosStatus()
    })
    
    return {
      currentEnv,
      loading,
      serverInfo,
      groupedServerInfoData,
      filteredGroupedServerInfoData,
      unknownVersionCount,
      showUnknownVersions,
      toggleUnknownVersions,
      fetchNacosStatus
    }
  }
}
</script>

<style scoped>
.blue-green-deploy {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}



.env-selector {
  margin-bottom: 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border: 1px solid #e1e8ed;
}

.env-radio-group {
  flex: 1;
}

.action-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.toggle-btn {
  border-radius: 8px;
  padding: 12px 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.refresh-btn {
  border-radius: 8px;
  padding: 12px 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
}

.env-selector :deep(.el-radio-group) {
  display: flex;
  gap: 8px;
}

.env-selector :deep(.el-radio-button__inner) {
  border-radius: 8px;
  padding: 12px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px solid #e1e8ed;
}

.env-selector :deep(.el-radio-button__inner:hover) {
  border-color: #409eff;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.2);
}

.env-selector :deep(.el-radio-button.is-active .el-radio-button__inner) {
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  border-color: #409eff;
  color: white;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}



.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.loading-container p {
  margin-top: 16px;
  color: #606266;
  font-size: 16px;
}

.table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.table-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.table-stats {
  display: flex;
  gap: 12px;
}

.main-table {
  border-radius: 0;
}

.main-table :deep(.el-table__header) {
  background: #f8f9fa;
}

.main-table :deep(.el-table__row:hover) {
  background-color: #f0f9ff;
}

.git-version-cell {
  display: flex;
  align-items: center;
}

.git-version-cell .el-tag {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 600;
}

.count-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.count-number {
  font-size: 18px;
  font-weight: 700;
  color: #409eff;
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.count-unit {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.service-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  max-height: 120px;
  overflow-y: auto;
}

.service-tag {
  margin: 0;
  border-radius: 6px;
  font-size: 12px;
  transition: all 0.2s ease;
}

.service-tag:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.expand-content {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 16px;
}

.expand-content h4 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.detail-table {
  border-radius: 8px;
  overflow: hidden;
}

.detail-table :deep(.el-table__header) {
  background: #e9ecef;
}

.ip-code {
  background: #f1f3f4;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #6c757d;
  border: 1px solid #dee2e6;
}

.build-date {
  color: #6c757d;
  font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .blue-green-deploy {
    padding: 16px;
  }

  .env-selector {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .env-selector :deep(.el-radio-group) {
    flex-direction: column;
  }

  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .service-tags {
    max-height: 80px;
  }
}
</style>