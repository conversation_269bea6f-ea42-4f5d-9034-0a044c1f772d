<template>
  <div class="db-sync-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span class="card-title">MySQL 数据同步</span>
            </div>
          </template>
          <p>从身边云 MySQL 同步到易畅行 MySQL sale 库</p>

          <!-- 选择需要同步的表 -->
          <div class="table-selection">
            <h3>选择需要同步的表</h3>
            <el-checkbox-group v-model="selectedTables">
              <div v-for="table in tables" :key="table.name" class="table-item">
                <el-checkbox :label="table.name">{{ table.name }}</el-checkbox>
              </div>
            </el-checkbox-group>
          </div>

          <!-- 同步按钮 -->
          <div class="sync-button">
            <el-button type="primary" @click="startSync" :loading="isSyncing">
              {{ isSyncing ? '同步中...' : '开始同步' }}
            </el-button>
          </div>

          <!-- 同步状态 -->
          <div v-if="syncStatus" class="sync-status">
            <h3>同步状态</h3>
            <el-alert
              :title="syncStatus"
              type="success"
              :closable="false"
              show-icon
            />
          </div>

          <!-- 错误提示 -->
          <div v-if="error" class="error">
            <h3>错误</h3>
            <el-alert
              :title="error"
              type="error"
              :closable="false"
              show-icon
            />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
const tables = [
  { name: 'emax_depart' },
  { name: 'emax_saler' },
  { name: 'sale_role' },
  { name: 'sale_role_permission' },
  { name: 'sale_user_role' }
]

// 用户选择的表
const selectedTables = ref(tables.map(table => table.name))
// 同步状态
const syncStatus = ref('')
// 是否正在同步
const isSyncing = ref(false)
// 错误信息
const error = ref('')
// 开始同步
const startSync = async () => {
  if (selectedTables.value.length === 0) {
    error.value = '请选择至少一张表'
    return
  }

  isSyncing.value = true
  error.value = ''
  syncStatus.value = '开始同步...'

  try {
    const response = await fetch('/api/dbsync', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json;charset=UTF-8'
      },
      body: JSON.stringify({
        tables: selectedTables.value.join(',')
      })
    })

    const data = await response.json()
    if (data.code === 200) {
      syncStatus.value = data.message
      ElMessage.success(data.message || '同步成功')
    } else {
      error.value = data.message || '同步失败，请重试'
      syncStatus.value = ''
      ElMessage.error(data.message || '同步失败')
    }
  } catch (error) {
    console.error('同步失败:', error)
    error.value = '同步失败，请重试'
    syncStatus.value = ''
    ElMessage.error('网络错误，同步失败')
  } finally {
    isSyncing.value = false
  }
}
</script>

<style scoped>
.db-sync-container {
  padding: 20px;
}



.table-selection {
  margin: 20px 0;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.table-item {
  margin: 10px 0;
}

.sync-button {
  margin: 20px 0;
}

.sync-status,
.error {
  margin-top: 20px;
}
</style>