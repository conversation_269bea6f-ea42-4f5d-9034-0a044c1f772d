<template>
  <div class="operation-logs">
    <!-- 搜索表单 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户ID">
          <el-input v-model="searchForm.user_id" placeholder="请输入用户ID" clearable />
        </el-form-item>
        <el-form-item label="模块">
          <el-input v-model="searchForm.module" placeholder="请输入模块名称" clearable />
        </el-form-item>
        <el-form-item label="功能名称">
          <el-input v-model="searchForm.function_name" placeholder="请输入功能名称" clearable />
        </el-form-item>
        <el-form-item label="开始日期">
          <el-date-picker
            v-model="searchForm.start_date"
            type="date"
            placeholder="选择开始日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="结束日期">
          <el-date-picker
            v-model="searchForm.end_date"
            type="date"
            placeholder="选择结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card" shadow="never">
      <el-table :data="tableData" border stripe>
        <el-table-column prop="user_id" label="用户ID" width="72" />
        <el-table-column prop="username" label="用户" width="100" />
        <el-table-column prop="module" label="模块" width="90" />
        <el-table-column prop="function_name" label="功能名称" width="180" />
        <el-table-column label="操作" width="140">
          <template #default="{ row }">
            {{ row.request_method }} {{ row.request_path }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status_code)">
              {{ row.status_code }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="响应消息" width="100" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.response_message || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="请求参数" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.request_params || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="execution_time" label="执行时间(s)" width="110">
          <template #default="{ row }">
            {{ row.execution_time ? row.execution_time.toFixed(4) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="ip_address" label="IP地址" width="132" />
        <el-table-column prop="created_at" label="操作时间" width="162" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getOperationLogs } from '../../api/permission'

// 搜索表单数据
const searchForm = ref({
  user_id: '',
  module: '',
  function_name: '',
  start_date: '',
  end_date: ''
})

// 表格数据
const tableData = ref([])
const total = ref(0)

// 分页参数
const pagination = ref({
  pageNum: 1,
  pageSize: 10
})

// 根据状态码获取标签类型
const getStatusType = (statusCode) => {
  if (statusCode >= 200 && statusCode < 300) {
    return 'success'
  } else if (statusCode >= 400 && statusCode < 500) {
    return 'warning'
  } else if (statusCode >= 500) {
    return 'danger'
  } else {
    return 'info'
  }
}

// 获取操作日志列表数据
const getOperationLogList = async () => {
  try {
    const params = {
      ...searchForm.value,
      ...pagination.value
    }
    const response = await getOperationLogs(params)
    if (response.data.code === 200) {
      tableData.value = response.data.data.list
      total.value = response.data.data.total
    } else {
      ElMessage.error(response.data.message || '获取操作日志失败')
    }
  } catch (error) {
    console.error('获取操作日志失败:', error)
    ElMessage.error('获取操作日志失败')
  }
}

// 搜索
const handleSearch = () => {
  pagination.value.pageNum = 1
  getOperationLogList()
}

// 重置
const handleReset = () => {
  searchForm.value = {
    user_id: '',
    module: '',
    function_name: '',
    start_date: '',
    end_date: ''
  }
  pagination.value = {
    pageNum: 1,
    pageSize: 10
  }
  getOperationLogList()
}

// 分页大小改变
const handleSizeChange = (val) => {
  pagination.value.pageSize = val
  getOperationLogList()
}

// 页码改变
const handleCurrentChange = (val) => {
  pagination.value.pageNum = val
  getOperationLogList()
}

// 页面加载时获取数据
onMounted(() => {
  getOperationLogList()
})
</script>

<style scoped>
.operation-logs {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
