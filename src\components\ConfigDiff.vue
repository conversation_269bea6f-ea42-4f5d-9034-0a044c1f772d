<template>
  <div class="config-diff-container">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="15" animated />
    </div>
    <div v-else>
      <div class="diff-header">
        <div class="diff-title">
          <h3>配置文件差异对比</h3>
          <span class="file-path">{{ filePath }}</span>
        </div>
        <div class="diff-actions">
          <el-switch
            v-model="showOnlyDiffs"
            active-text="仅显示差异"
            inactive-text="显示全部"
          />
        </div>
      </div>
      
      <div class="diff-content">
        <div class="diff-servers">
          <div v-for="(server, index) in servers" :key="index" class="server-label">
            <el-tag :type="index === 0 ? 'primary' : 'success'">
              {{ server }}
            </el-tag>
          </div>
        </div>
        
        <div class="diff-body">
          <table class="diff-table">
            <tbody>
              <tr v-for="(line, lineIndex) in diffLines" :key="lineIndex" 
                  :class="{'diff-hidden': showOnlyDiffs && !line.hasDiff}">
                <td class="line-number">{{ lineIndex + 1 }}</td>
                <td v-for="(content, serverIndex) in line.contents" :key="serverIndex"
                    :class="{'diff-highlight': line.hasDiff && content !== line.contents[0]}">
                  <pre>{{ content || ' ' }}</pre>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <div v-if="diffSummary.totalDiffs > 0" class="diff-summary">
        <el-alert
          type="warning"
          :title="`发现 ${diffSummary.totalDiffs} 处配置差异`"
          show-icon
        >
          <div class="summary-details">
            <p>差异行数: {{ diffSummary.diffLines }} 行</p>
            <p>总行数: {{ diffSummary.totalLines }} 行</p>
          </div>
        </el-alert>
      </div>
      <div v-else class="diff-summary">
        <el-alert
          type="success"
          title="配置完全一致"
          show-icon
        />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'

export default {
  name: 'ConfigDiff',
  props: {
    filePath: {
      type: String,
      default: ''
    },
    servers: {
      type: Array,
      default: () => []
    },
    contents: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const showOnlyDiffs = ref(false)
    
    // 处理差异行
    const diffLines = computed(() => {
      if (!props.contents || props.contents.length < 2) {
        return []
      }
      
      // 获取第一个服务器的内容作为基准
      const baseContent = props.contents[0] || ''
      const baseLines = baseContent.split('\n')
      
      // 处理所有服务器的内容行
      return baseLines.map((baseLine, lineIndex) => {
        const lineContents = props.contents.map(content => {
          const lines = content.split('\n')
          return lines[lineIndex] || ''
        })
        
        // 检查这一行是否有差异
        const hasDiff = lineContents.some(content => content !== lineContents[0])
        
        return {
          contents: lineContents,
          hasDiff
        }
      })
    })
    
    // 差异统计
    const diffSummary = computed(() => {
      const diffLinesCount = diffLines.value.filter(line => line.hasDiff).length
      return {
        totalDiffs: diffLinesCount,
        diffLines: diffLinesCount,
        totalLines: diffLines.value.length
      }
    })
    
    return {
      showOnlyDiffs,
      diffLines,
      diffSummary
    }
  }
}
</script>

<style scoped>
.config-diff-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.loading-container {
  padding: 20px 0;
}

.diff-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.diff-title {
  display: flex;
  flex-direction: column;
}

.diff-title h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.file-path {
  font-size: 14px;
  color: #909399;
}

.diff-servers {
  display: flex;
  margin-bottom: 10px;
}

.server-label {
  margin-right: 15px;
}

.diff-body {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow-x: auto;
  margin-bottom: 20px;
}

.diff-table {
  width: 100%;
  border-collapse: collapse;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
}

.diff-table td {
  padding: 4px 8px;
  vertical-align: top;
  white-space: pre-wrap;
  word-break: break-all;
}

.line-number {
  width: 40px;
  text-align: right;
  color: #909399;
  background-color: #f5f7fa;
  border-right: 1px solid #ebeef5;
  user-select: none;
}

.diff-highlight {
  background-color: #ffeef0;
  color: #f56c6c;
}

.diff-hidden {
  display: none;
}

.diff-summary {
  margin-top: 20px;
}

.summary-details {
  margin-top: 10px;
  font-size: 14px;
}

.summary-details p {
  margin: 5px 0;
}

pre {
  margin: 0;
  white-space: pre-wrap;
}
</style>