# 服务器管理系统前端

基于Vue3的服务器管理前端系统，提供服务器监控、配置管理和运维操作等功能。

## 🚀 功能特性
- 服务器状态实时监控
- 运维任务调度管理
- 用户权限控制系统
- 可视化配置管理界面
- 操作日志审计功能

## 🛠️ 技术栈
- **前端框架**: Vue 3.3
- **构建工具**: Vite 5.0
- **UI组件库**: Element Plus 2.4
- **状态管理**: Vuex 4.1
- **路由管理**: Vue Router 4.2
- **HTTP客户端**: Axios 1.6

## 📦 环境要求
- Node.js 16+
- npm 8+

## 🔧 安装指南
```bash
# 克隆项目
git clone http://43.254.89.5:58880/guoyabin/iyunwei-vue.git

# 安装依赖
npm install

# 开发模式
npm run dev

# 生产构建
npm run build


iyunwei-vue/
├── deploy/
│   ├── Jenkinsfile      # 持续集成配置
├── src/
│   ├── assets/      # 静态资源
│   ├── components/  # 通用组件
│   ├── router/      # 路由配置
│   ├── store/       # Vuex状态管理
│   ├── views/       # 页面视图
│   └── utils/       # 工具函数
└── package.json     # 依赖管理
```