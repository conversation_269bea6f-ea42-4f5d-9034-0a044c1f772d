<template>
  <div class="webhook-config-container">
    <!-- 页面标题和操作按钮 -->
    <el-card class="header-card">
      <div class="card-header">
        <span class="card-title">Webhook配置管理</span>
        <div class="header-actions">
          <el-button
            type="primary"
            @click="showCreateDialog"
            :icon="Plus"
          >
            新增配置
          </el-button>
          <el-button
            @click="loadWebhookConfigs"
            :loading="loading"
            :icon="Refresh"
          >
            刷新
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 配置列表 -->
    <el-card class="config-list-card">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="8" animated />
      </div>

      <div v-else-if="configList.length === 0" class="empty-container">
        <el-empty description="暂无Webhook配置" />
      </div>

      <div v-else class="config-table-container">
        <el-table
          :data="configList"
          style="width: 100%"
          stripe
          border
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="project_category" label="项目分类" width="120" />
          <el-table-column prop="ip_pattern" label="IP模式" width="150" />
          <el-table-column prop="datacenter" label="数据中心" width="120" />
          <el-table-column prop="environment" label="环境" width="100" />
          <el-table-column prop="group_name" label="群组名称" width="150" />
          <el-table-column prop="webhook_url" label="Webhook URL" min-width="200" show-overflow-tooltip />
          <el-table-column prop="is_active" label="状态" width="100">
            <template #default="scope">
              <el-switch
                v-model="scope.row.is_active"
                @change="toggleConfig(scope.row)"
                :loading="toggleLoading.includes(scope.row.id)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="180" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                @click="showEditDialog(scope.row)"
                :icon="Edit"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="deleteConfig(scope.row)"
                :loading="deleteLoading.includes(scope.row.id)"
                :icon="Delete"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-area" v-if="configList.length > 0">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑Webhook配置' : '新增Webhook配置'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="项目分类" prop="project_category">
          <el-input
            v-model="formData.project_category"
            placeholder="请输入项目分类"
          />
        </el-form-item>
        
        <el-form-item label="IP模式" prop="ip_pattern">
          <el-input
            v-model="formData.ip_pattern"
            placeholder="例如: 10.0.2.*"
          />
        </el-form-item>
        
        <el-form-item label="数据中心" prop="datacenter">
          <el-select
            v-model="formData.datacenter"
            placeholder="请选择数据中心"
            style="width: 100%"
          >
            <el-option label="亦庄机房" value="亦庄机房" />
            <el-option label="联通云机房" value="联通云机房" />
            <el-option label="易畅行机房" value="易畅行机房" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="环境" prop="environment">
          <el-select
            v-model="formData.environment"
            placeholder="请选择环境"
            style="width: 100%"
          >
            <el-option label="物理机" value="物理机" />
            <el-option label="容器" value="容器" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="群组名称" prop="group_name">
          <el-input
            v-model="formData.group_name"
            placeholder="请输入群组名称"
          />
        </el-form-item>
        
        <el-form-item label="Webhook URL" prop="webhook_url">
          <el-input
            v-model="formData.webhook_url"
            type="textarea"
            :rows="3"
            placeholder="请输入完整的Webhook URL"
          />
        </el-form-item>
        
        <el-form-item label="启用状态" prop="is_active">
          <el-switch
            v-model="formData.is_active"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="submitForm"
            :loading="submitLoading"
          >
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  Edit,
  Delete
} from '@element-plus/icons-vue'
import {
  getWebhookConfigs,
  createWebhookConfig,
  updateWebhookConfig,
  deleteWebhookConfig
} from '../api/alertmanager'

export default {
  name: 'AlertManagerWebhookConfig',
  components: {
    Plus,
    Refresh,
    Edit,
    Delete
  },
  setup() {
    const loading = ref(false)
    const submitLoading = ref(false)
    const toggleLoading = ref([])
    const deleteLoading = ref([])
    
    // 分页
    const currentPage = ref(1)
    const pageSize = ref(20)
    const total = ref(0)
    
    // 数据
    const configList = ref([])
    
    // 对话框
    const dialogVisible = ref(false)
    const isEdit = ref(false)
    const formRef = ref(null)
    
    // 表单数据
    const formData = reactive({
      project_category: '',
      ip_pattern: '',
      datacenter: '',
      environment: '',
      group_name: '',
      webhook_url: '',
      is_active: true
    })
    
    // 表单验证规则
    const formRules = {
      project_category: [
        { required: true, message: '请输入项目分类', trigger: 'blur' }
      ],
      ip_pattern: [
        { required: true, message: '请输入IP模式', trigger: 'blur' }
      ],
      datacenter: [
        { required: true, message: '请选择数据中心', trigger: 'change' }
      ],
      environment: [
        { required: true, message: '请选择环境', trigger: 'change' }
      ],
      group_name: [
        { required: true, message: '请输入群组名称', trigger: 'blur' }
      ],
      webhook_url: [
        { required: true, message: '请输入Webhook URL', trigger: 'blur' },
        { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
      ]
    }

    // 加载Webhook配置列表
    const loadWebhookConfigs = async () => {
      loading.value = true
      try {
        const params = {
          page: currentPage.value,
          per_page: pageSize.value
        }

        const response = await getWebhookConfigs(params)

        if (response.data.success) {
          configList.value = response.data.data.items || []
          total.value = response.data.data.total || 0
        } else {
          ElMessage.error(response.data.message || '获取配置列表失败')
        }
      } catch (error) {
        ElMessage.error('获取配置列表失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loading.value = false
      }
    }

    // 显示创建对话框
    const showCreateDialog = () => {
      isEdit.value = false
      resetForm()
      dialogVisible.value = true
    }

    // 显示编辑对话框
    const showEditDialog = (config) => {
      isEdit.value = true
      Object.assign(formData, config)
      dialogVisible.value = true
    }

    // 重置表单
    const resetForm = () => {
      Object.assign(formData, {
        project_category: '',
        ip_pattern: '',
        datacenter: '',
        environment: '',
        group_name: '',
        webhook_url: '',
        is_active: true
      })
      if (formRef.value) {
        formRef.value.clearValidate()
      }
    }

    // 提交表单
    const submitForm = async () => {
      if (!formRef.value) return

      try {
        await formRef.value.validate()

        submitLoading.value = true

        let response
        if (isEdit.value) {
          response = await updateWebhookConfig(formData.id, formData)
        } else {
          response = await createWebhookConfig(formData)
        }

        if (response.data.success) {
          ElMessage.success(response.data.message || `${isEdit.value ? '更新' : '创建'}成功`)
          dialogVisible.value = false
          loadWebhookConfigs()
        } else {
          ElMessage.error(response.data.message || `${isEdit.value ? '更新' : '创建'}失败`)
        }
      } catch (error) {
        if (error !== false) { // 不是表单验证错误
          ElMessage.error(`${isEdit.value ? '更新' : '创建'}失败: ` + (error.response?.data?.message || error.message))
        }
      } finally {
        submitLoading.value = false
      }
    }

    // 切换配置状态
    const toggleConfig = async (config) => {
      toggleLoading.value.push(config.id)

      try {
        const response = await updateWebhookConfig(config.id, {
          ...config,
          is_active: config.is_active
        })

        if (response.data.success) {
          ElMessage.success(`配置已${config.is_active ? '启用' : '禁用'}`)
        } else {
          // 恢复原状态
          config.is_active = !config.is_active
          ElMessage.error(response.data.message || '状态切换失败')
        }
      } catch (error) {
        // 恢复原状态
        config.is_active = !config.is_active
        ElMessage.error('状态切换失败: ' + (error.response?.data?.message || error.message))
      } finally {
        toggleLoading.value = toggleLoading.value.filter(id => id !== config.id)
      }
    }

    // 删除配置
    const deleteConfig = async (config) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除配置 "${config.group_name}" 吗？此操作不可恢复。`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        deleteLoading.value.push(config.id)

        const response = await deleteWebhookConfig(config.id)

        if (response.data.success) {
          ElMessage.success('配置删除成功')
          loadWebhookConfigs()
        } else {
          ElMessage.error(response.data.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败: ' + (error.response?.data?.message || error.message))
        }
      } finally {
        deleteLoading.value = deleteLoading.value.filter(id => id !== config.id)
      }
    }

    // 分页处理
    const handleSizeChange = (val) => {
      pageSize.value = val
      currentPage.value = 1
      loadWebhookConfigs()
    }

    const handleCurrentChange = (val) => {
      currentPage.value = val
      loadWebhookConfigs()
    }

    // 初始化
    onMounted(() => {
      loadWebhookConfigs()
    })

    return {
      loading,
      submitLoading,
      toggleLoading,
      deleteLoading,
      currentPage,
      pageSize,
      total,
      configList,
      dialogVisible,
      isEdit,
      formRef,
      formData,
      formRules,
      loadWebhookConfigs,
      showCreateDialog,
      showEditDialog,
      resetForm,
      submitForm,
      toggleConfig,
      deleteConfig,
      handleSizeChange,
      handleCurrentChange
    }
  }
}
</script>

<style scoped>
.webhook-config-container {
  padding: var(--spacing-lg);
  background: var(--bg-page);
  min-height: calc(100vh - 60px);
}

.header-card, .config-list-card {
  margin-bottom: var(--spacing-lg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.loading-container {
  padding: calc(var(--spacing-xl) + var(--spacing-sm));
}

.empty-container {
  padding: calc(var(--spacing-xl) + var(--spacing-sm));
  text-align: center;
}

.config-table-container {
  padding: var(--spacing-md) 0;
}

.pagination-area {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-lg);
  padding: var(--spacing-lg) 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

/* 表格样式增强 */
.el-table {
  border-radius: var(--border-radius-base);
  overflow: hidden;
}

.el-table .el-table__header-wrapper {
  border-radius: var(--border-radius-base) var(--border-radius-base) 0 0;
}

.el-table .cell {
  padding: var(--spacing-sm) var(--spacing-md);
}

/* 表单样式增强 */
.el-form-item {
  margin-bottom: var(--spacing-lg);
}

.el-form-item__label {
  font-weight: 500;
  color: var(--text-primary);
}

.el-input, .el-select {
  width: 100%;
}

.el-textarea .el-textarea__inner {
  resize: vertical;
  min-height: 80px;
}

/* 开关样式 */
.el-switch {
  --el-switch-on-color: var(--success-color);
  --el-switch-off-color: var(--text-placeholder);
}

/* 按钮样式增强 */
.el-button {
  border-radius: var(--border-radius-base);
  transition: all var(--transition-base);
}

.el-button:hover {
  transform: translateY(-1px);
}

.el-button--primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border: none;
}

.el-button--danger {
  background: linear-gradient(135deg, var(--danger-color), #e53e3e);
  border: none;
}

/* 对话框样式 */
.el-dialog {
  border-radius: var(--border-radius-large);
}

.el-dialog__header {
  padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
  border-bottom: 1px solid var(--border-lighter);
}

.el-dialog__body {
  padding: var(--spacing-lg);
}

.el-dialog__footer {
  padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg);
  border-top: 1px solid var(--border-lighter);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .webhook-config-container {
    padding: var(--spacing-sm);
  }

  .card-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .config-table-container {
    overflow-x: auto;
  }

  .el-table {
    min-width: 800px;
  }

  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .dialog-footer {
    flex-direction: column;
  }

  .dialog-footer .el-button {
    width: 100%;
    margin: 0;
  }
}

@media (max-width: 480px) {
  .el-form-item__label {
    width: 100% !important;
    text-align: left !important;
    margin-bottom: var(--spacing-xs);
  }

  .el-form-item__content {
    margin-left: 0 !important;
  }
}
</style>
