<template>
  <AppLayout
    :menu-list="menuList"
    :user-info="userInfo"
    :active-menu="activeMenu"
    @menu-select="handleSelect"
    @user-command="handleCommand"
  >
    <router-view v-if="$route.path !== '/'" />
    <div v-else class="welcome-container">
      <h1>欢迎使用运维管理系统</h1>
      <p>当前用户：{{ userInfo.username }}</p>
      <p>登录时间：{{ userInfo.lastLoginTime }}</p>
    </div>
  </AppLayout>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { resetDynamicRoutes } from '../router'
import AppLayout from '../components/layout/AppLayout.vue'

export default {
  name: 'Dashboard',
  components: {
    AppLayout
  },
  setup() {
    const router = useRouter()
    const menuList = ref([])
    const activeMenu = ref('')
    const userInfo = ref({
      username: '',
      lastLoginTime: ''
    })

    const getMenuList = async () => {
      try {
        const response = await fetch('/api/user/menus', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })
        if (response.status === 401) {
          console.error('用户未登录或登录已过期')
          localStorage.removeItem('token')
          router.push('/login')
          return false
        }
        const data = await response.json()
        if (data.data) {
          menuList.value = data.data
          return true
        } else {
          console.error('获取菜单数据格式错误')
          return false
        }
      } catch (error) {
        console.error('获取菜单列表失败:', error)
        return false
      }
    }

    // 获取用户信息
    const getUserInfo = async () => {
      try {
        const response = await fetch('/api/user/info', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })
        const data = await response.json()
        userInfo.value = {
          username: data.data.username,
          lastLoginTime: data.data.lastLoginTime
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    }

    // 菜单选择处理
    const handleSelect = (index) => {
      activeMenu.value = index
      // 根据菜单项的路径进行跳转
      const menuItem = findMenuItem(menuList.value, index)
      if (menuItem && menuItem.path) {
        // 如果是父级菜单（有子菜单），不进行路由跳转
        if (menuItem.children && menuItem.children.length > 0) {
          console.log(`父级菜单 "${menuItem.title}" 被点击，不进行路由跳转`)
          return
        }
        // 只有叶子节点菜单才进行路由跳转
        router.push(menuItem.path)
      }
    }

    // 递归查找菜单项
    const findMenuItem = (items, id) => {
      for (const item of items) {
        if (item.id === id) {
          return item
        }
        if (item.children) {
          const found = findMenuItem(item.children, id)
          if (found) return found
        }
      }
      return null
    }

    // 处理下拉菜单命令
    const handleCommand = (command) => {
      if (command === 'logout') {
        localStorage.removeItem('token')
        resetDynamicRoutes() // 重置动态路由状态
        router.push('/login')
      }
    }

    onMounted(async () => {
      // 获取菜单数据用于显示侧边栏
      const menuSuccess = await getMenuList()
      // 获取用户信息
      if (menuSuccess && localStorage.getItem('token')) {
        getUserInfo()
      }
    })

    return {
      menuList,
      activeMenu,
      userInfo,
      handleSelect,
      handleCommand
    }
  }
}
</script>

<style scoped>
.welcome-container {
  text-align: center;
  padding: 60px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  margin: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.welcome-container h1 {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 24px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-container p {
  font-size: 16px;
  margin: 12px 0;
  opacity: 0.9;
}
</style>