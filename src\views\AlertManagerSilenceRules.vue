<template>
  <div class="silence-rules-container">
    <!-- 页面标题和操作按钮 -->
    <el-card class="header-card">
      <div class="card-header">
        <span class="card-title">静默规则管理</span>
        <div class="header-actions">
          <el-button
            type="primary"
            @click="showCreateDialog"
            :icon="Plus"
          >
            新增规则
          </el-button>
          <el-button
            @click="loadSilenceRules"
            :loading="loading"
            :icon="Refresh"
          >
            刷新
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 规则列表 -->
    <el-card class="rules-list-card">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="8" animated />
      </div>

      <div v-else-if="rulesList.length === 0" class="empty-container">
        <el-empty description="暂无静默规则" />
      </div>

      <div v-else class="rules-table-container">
        <el-table
          :data="rulesList"
          style="width: 100%"
          stripe
          border
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="rule_name" label="规则名称" width="150" />
          <el-table-column prop="alert_title_pattern" label="告警标题模式" width="200" show-overflow-tooltip />
          <el-table-column prop="project_category" label="项目分类" width="120" />
          <el-table-column prop="alert_level" label="告警等级" width="100">
            <template #default="scope">
              <el-tag
                v-if="scope.row.alert_level"
                :type="getLevelType(scope.row.alert_level)"
                size="small"
              >
                {{ getLevelText(scope.row.alert_level) }}
              </el-tag>
              <span v-else class="text-placeholder">全部</span>
            </template>
          </el-table-column>
          <el-table-column prop="ip_pattern" label="IP模式" width="150" />
          <el-table-column prop="silence_hours" label="静默时长" width="100">
            <template #default="scope">
              {{ scope.row.silence_hours }}小时
            </template>
          </el-table-column>
          <el-table-column prop="start_time" label="开始时间" width="180" />
          <el-table-column prop="end_time" label="结束时间" width="180" />
          <el-table-column prop="is_silenced" label="静默状态" width="100">
            <template #default="scope">
              <el-tag
                :type="scope.row.is_silenced ? 'warning' : 'info'"
                size="small"
              >
                {{ scope.row.is_silenced ? '静默中' : '已结束' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="is_active" label="启用状态" width="100">
            <template #default="scope">
              <el-switch
                v-model="scope.row.is_active"
                @change="toggleRule(scope.row)"
                :loading="toggleLoading.includes(scope.row.id)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="reason" label="静默原因" min-width="150" show-overflow-tooltip />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                @click="showEditDialog(scope.row)"
                :icon="Edit"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="deleteRule(scope.row)"
                :loading="deleteLoading.includes(scope.row.id)"
                :icon="Delete"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-area" v-if="rulesList.length > 0">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑静默规则' : '新增静默规则'"
      width="700px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="140px"
      >
        <el-form-item label="规则名称" prop="rule_name">
          <el-input
            v-model="formData.rule_name"
            placeholder="请输入规则名称"
          />
        </el-form-item>
        
        <el-form-item label="告警标题模式" prop="alert_title_pattern">
          <el-input
            v-model="formData.alert_title_pattern"
            placeholder="支持正则表达式，例如: .*维护.*"
          />
          <div class="form-tip">
            支持正则表达式匹配告警标题，例如 ".*维护.*" 匹配包含"维护"的告警
          </div>
        </el-form-item>
        
        <el-form-item label="项目分类" prop="project_category">
          <el-input
            v-model="formData.project_category"
            placeholder="请输入项目分类，留空表示全部"
          />
        </el-form-item>
        
        <el-form-item label="告警等级" prop="alert_level">
          <el-select
            v-model="formData.alert_level"
            placeholder="选择告警等级，留空表示全部"
            clearable
            style="width: 100%"
          >
            <el-option label="严重" value="critical" />
            <el-option label="警告" value="warning" />
            <el-option label="信息" value="info" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="IP模式" prop="ip_pattern">
          <el-input
            v-model="formData.ip_pattern"
            placeholder="例如: 10.0.2.*，留空表示全部"
          />
        </el-form-item>
        
        <el-form-item label="静默时长(小时)" prop="silence_hours">
          <el-input-number
            v-model="formData.silence_hours"
            :min="1"
            :max="168"
            placeholder="1-168小时"
            style="width: 100%"
          />
          <div class="form-tip">
            静默时长范围：1-168小时（7天）
          </div>
        </el-form-item>
        
        <el-form-item label="静默原因" prop="reason">
          <el-input
            v-model="formData.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入静默原因"
          />
        </el-form-item>
        
        <el-form-item label="启用状态" prop="is_active">
          <el-switch
            v-model="formData.is_active"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="submitForm"
            :loading="submitLoading"
          >
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  Edit,
  Delete
} from '@element-plus/icons-vue'
import {
  getSilenceRules,
  createSilenceRule,
  updateSilenceRule,
  deleteSilenceRule,
  toggleSilenceRule
} from '../api/alertmanager'

export default {
  name: 'AlertManagerSilenceRules',
  components: {
    Plus,
    Refresh,
    Edit,
    Delete
  },
  setup() {
    const loading = ref(false)
    const submitLoading = ref(false)
    const toggleLoading = ref([])
    const deleteLoading = ref([])
    
    // 分页
    const currentPage = ref(1)
    const pageSize = ref(20)
    const total = ref(0)
    
    // 数据
    const rulesList = ref([])
    
    // 对话框
    const dialogVisible = ref(false)
    const isEdit = ref(false)
    const formRef = ref(null)
    
    // 表单数据
    const formData = reactive({
      rule_name: '',
      alert_title_pattern: '',
      project_category: '',
      alert_level: '',
      ip_pattern: '',
      silence_hours: 4,
      reason: '',
      is_active: true
    })
    
    // 表单验证规则
    const formRules = {
      rule_name: [
        { required: true, message: '请输入规则名称', trigger: 'blur' }
      ],
      alert_title_pattern: [
        { required: true, message: '请输入告警标题模式', trigger: 'blur' }
      ],
      silence_hours: [
        { required: true, message: '请输入静默时长', trigger: 'blur' },
        { type: 'number', min: 1, max: 168, message: '静默时长必须在1-168小时之间', trigger: 'blur' }
      ],
      reason: [
        { required: true, message: '请输入静默原因', trigger: 'blur' }
      ]
    }

    // 加载静默规则列表
    const loadSilenceRules = async () => {
      loading.value = true
      try {
        const params = {
          page: currentPage.value,
          per_page: pageSize.value
        }

        const response = await getSilenceRules(params)

        if (response.data.success) {
          rulesList.value = response.data.data.items || []
          total.value = response.data.data.total || 0
        } else {
          ElMessage.error(response.data.message || '获取规则列表失败')
        }
      } catch (error) {
        ElMessage.error('获取规则列表失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loading.value = false
      }
    }

    // 显示创建对话框
    const showCreateDialog = () => {
      isEdit.value = false
      resetForm()
      dialogVisible.value = true
    }

    // 显示编辑对话框
    const showEditDialog = (rule) => {
      isEdit.value = true
      Object.assign(formData, rule)
      dialogVisible.value = true
    }

    // 重置表单
    const resetForm = () => {
      Object.assign(formData, {
        rule_name: '',
        alert_title_pattern: '',
        project_category: '',
        alert_level: '',
        ip_pattern: '',
        silence_hours: 4,
        reason: '',
        is_active: true
      })
      if (formRef.value) {
        formRef.value.clearValidate()
      }
    }

    // 提交表单
    const submitForm = async () => {
      if (!formRef.value) return

      try {
        await formRef.value.validate()

        submitLoading.value = true

        let response
        if (isEdit.value) {
          response = await updateSilenceRule(formData.id, formData)
        } else {
          response = await createSilenceRule(formData)
        }

        if (response.data.success) {
          ElMessage.success(response.data.message || `${isEdit.value ? '更新' : '创建'}成功`)
          dialogVisible.value = false
          loadSilenceRules()
        } else {
          ElMessage.error(response.data.message || `${isEdit.value ? '更新' : '创建'}失败`)
        }
      } catch (error) {
        if (error !== false) { // 不是表单验证错误
          ElMessage.error(`${isEdit.value ? '更新' : '创建'}失败: ` + (error.response?.data?.message || error.message))
        }
      } finally {
        submitLoading.value = false
      }
    }

    // 切换规则状态
    const toggleRule = async (rule) => {
      toggleLoading.value.push(rule.id)

      try {
        const response = await toggleSilenceRule(rule.id, rule.is_active)

        if (response.data.success) {
          ElMessage.success(`规则已${rule.is_active ? '启用' : '禁用'}`)
        } else {
          // 恢复原状态
          rule.is_active = !rule.is_active
          ElMessage.error(response.data.message || '状态切换失败')
        }
      } catch (error) {
        // 恢复原状态
        rule.is_active = !rule.is_active
        ElMessage.error('状态切换失败: ' + (error.response?.data?.message || error.message))
      } finally {
        toggleLoading.value = toggleLoading.value.filter(id => id !== rule.id)
      }
    }

    // 删除规则
    const deleteRule = async (rule) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除规则 "${rule.rule_name}" 吗？此操作不可恢复。`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        deleteLoading.value.push(rule.id)

        const response = await deleteSilenceRule(rule.id)

        if (response.data.success) {
          ElMessage.success('规则删除成功')
          loadSilenceRules()
        } else {
          ElMessage.error(response.data.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败: ' + (error.response?.data?.message || error.message))
        }
      } finally {
        deleteLoading.value = deleteLoading.value.filter(id => id !== rule.id)
      }
    }

    // 工具函数
    const getLevelType = (level) => {
      switch (level) {
        case 'critical':
          return 'danger'
        case 'warning':
          return 'warning'
        case 'info':
          return 'info'
        default:
          return 'info'
      }
    }

    const getLevelText = (level) => {
      switch (level) {
        case 'critical':
          return '严重'
        case 'warning':
          return '警告'
        case 'info':
          return '信息'
        default:
          return '未知'
      }
    }

    // 分页处理
    const handleSizeChange = (val) => {
      pageSize.value = val
      currentPage.value = 1
      loadSilenceRules()
    }

    const handleCurrentChange = (val) => {
      currentPage.value = val
      loadSilenceRules()
    }

    // 初始化
    onMounted(() => {
      loadSilenceRules()
    })

    return {
      loading,
      submitLoading,
      toggleLoading,
      deleteLoading,
      currentPage,
      pageSize,
      total,
      rulesList,
      dialogVisible,
      isEdit,
      formRef,
      formData,
      formRules,
      loadSilenceRules,
      showCreateDialog,
      showEditDialog,
      resetForm,
      submitForm,
      toggleRule,
      deleteRule,
      getLevelType,
      getLevelText,
      handleSizeChange,
      handleCurrentChange
    }
  }
}
</script>

<style scoped>
.silence-rules-container {
  padding: var(--spacing-lg);
  background: var(--bg-page);
  min-height: calc(100vh - 60px);
}

.header-card, .rules-list-card {
  margin-bottom: var(--spacing-lg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.loading-container {
  padding: calc(var(--spacing-xl) + var(--spacing-sm));
}

.empty-container {
  padding: calc(var(--spacing-xl) + var(--spacing-sm));
  text-align: center;
}

.rules-table-container {
  padding: var(--spacing-md) 0;
}

.pagination-area {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-lg);
  padding: var(--spacing-lg) 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

.form-tip {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
  line-height: 1.4;
}

.text-placeholder {
  color: var(--text-placeholder);
  font-style: italic;
}

/* 表格样式增强 */
.el-table {
  border-radius: var(--border-radius-base);
  overflow: hidden;
}

.el-table .el-table__header-wrapper {
  border-radius: var(--border-radius-base) var(--border-radius-base) 0 0;
}

.el-table .cell {
  padding: var(--spacing-sm) var(--spacing-md);
}

/* 表单样式增强 */
.el-form-item {
  margin-bottom: var(--spacing-lg);
}

.el-form-item__label {
  font-weight: 500;
  color: var(--text-primary);
}

.el-input, .el-select, .el-input-number {
  width: 100%;
}

.el-textarea .el-textarea__inner {
  resize: vertical;
  min-height: 80px;
}

/* 开关样式 */
.el-switch {
  --el-switch-on-color: var(--success-color);
  --el-switch-off-color: var(--text-placeholder);
}

/* 按钮样式增强 */
.el-button {
  border-radius: var(--border-radius-base);
  transition: all var(--transition-base);
}

.el-button:hover {
  transform: translateY(-1px);
}

.el-button--primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border: none;
}

.el-button--danger {
  background: linear-gradient(135deg, var(--danger-color), #e53e3e);
  border: none;
}

/* 标签样式 */
.el-tag {
  border-radius: var(--border-radius-base);
}

/* 对话框样式 */
.el-dialog {
  border-radius: var(--border-radius-large);
}

.el-dialog__header {
  padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
  border-bottom: 1px solid var(--border-lighter);
}

.el-dialog__body {
  padding: var(--spacing-lg);
}

.el-dialog__footer {
  padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg);
  border-top: 1px solid var(--border-lighter);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .silence-rules-container {
    padding: var(--spacing-sm);
  }

  .card-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .rules-table-container {
    overflow-x: auto;
  }

  .el-table {
    min-width: 1200px;
  }

  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .dialog-footer {
    flex-direction: column;
  }

  .dialog-footer .el-button {
    width: 100%;
    margin: 0;
  }
}

@media (max-width: 480px) {
  .el-form-item__label {
    width: 100% !important;
    text-align: left !important;
    margin-bottom: var(--spacing-xs);
  }

  .el-form-item__content {
    margin-left: 0 !important;
  }
}
</style>
