<template>
  <div class="alert-test-container">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">AlertManager 系统测试</span>
        </div>
      </template>

      <div class="test-content">
        <el-alert
          title="AlertManager 告警管理系统已创建完成"
          type="success"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>已成功创建以下功能模块：</p>
            <ul>
              <li>✅ 告警列表页面 - 支持分页、筛选、搜索和批量操作</li>
              <li>✅ 告警详情页面 - 显示完整告警信息和原始数据</li>
              <li>✅ Webhook配置管理 - 支持增删改查操作</li>
              <li>✅ 静默规则管理 - 支持创建和管理静默规则</li>
              <li>✅ 告警统计仪表板 - 显示统计数据和图表</li>
              <li>✅ API服务层 - 完整的后端接口封装</li>
              <li>✅ 路由配置 - 动态路由和菜单映射</li>
            </ul>
          </template>
        </el-alert>

        <div class="navigation-buttons">
          <h3>快速导航</h3>
          <el-row :gutter="16">
            <el-col :span="8">
              <el-button
                type="primary"
                @click="goToPage('/alertmanager/dashboard')"
                :icon="DataBoard"
                size="large"
                style="width: 100%; height: 60px;"
              >
                告警仪表板
              </el-button>
            </el-col>
            <el-col :span="8">
              <el-button
                type="success"
                @click="goToPage('/alertmanager/list')"
                :icon="List"
                size="large"
                style="width: 100%; height: 60px;"
              >
                告警列表
              </el-button>
            </el-col>
            <el-col :span="8">
              <el-button
                type="warning"
                @click="goToPage('/alertmanager/webhook-config')"
                :icon="Setting"
                size="large"
                style="width: 100%; height: 60px;"
              >
                Webhook配置
              </el-button>
            </el-col>
          </el-row>
          
          <el-row :gutter="16" style="margin-top: 16px;">
            <el-col :span="12">
              <el-button
                type="info"
                @click="goToPage('/alertmanager/silence-rules')"
                :icon="Mute"
                size="large"
                style="width: 100%; height: 60px;"
              >
                静默规则管理
              </el-button>
            </el-col>
            <el-col :span="12">
              <el-button
                type="danger"
                @click="testAPI"
                :icon="Connection"
                size="large"
                style="width: 100%; height: 60px;"
                :loading="testing"
              >
                测试API连接
              </el-button>
            </el-col>
          </el-row>
        </div>

        <div class="api-info">
          <h3>API 接口信息</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="基础URL">
              http://iyunwei.serviceshare.com/api
            </el-descriptions-item>
            <el-descriptions-item label="告警接收">
              POST /v2/alerts (无需认证)
            </el-descriptions-item>
            <el-descriptions-item label="告警查询">
              GET /alertmanager/logs (需要JWT认证)
            </el-descriptions-item>
            <el-descriptions-item label="告警管理">
              PUT /alertmanager/logs/{id}/resolve (需要权限)
            </el-descriptions-item>
            <el-descriptions-item label="配置管理">
              /alertmanager/config/* (需要权限)
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="features-info">
          <h3>功能特性</h3>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-card class="feature-card">
                <h4>🔍 告警查询</h4>
                <ul>
                  <li>支持多条件筛选</li>
                  <li>分页显示</li>
                  <li>实时状态更新</li>
                  <li>批量操作</li>
                </ul>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card class="feature-card">
                <h4>⚙️ 配置管理</h4>
                <ul>
                  <li>Webhook配置</li>
                  <li>静默规则</li>
                  <li>机房环境映射</li>
                  <li>权限控制</li>
                </ul>
              </el-card>
            </el-col>
          </el-row>
          
          <el-row :gutter="16" style="margin-top: 16px;">
            <el-col :span="12">
              <el-card class="feature-card">
                <h4>📊 统计分析</h4>
                <ul>
                  <li>实时统计数据</li>
                  <li>图表可视化</li>
                  <li>趋势分析</li>
                  <li>分类统计</li>
                </ul>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card class="feature-card">
                <h4>📱 响应式设计</h4>
                <ul>
                  <li>移动端适配</li>
                  <li>桌面端优化</li>
                  <li>触摸友好</li>
                  <li>跨浏览器兼容</li>
                </ul>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  DataBoard,
  List,
  Setting,
  Mute,
  Connection
} from '@element-plus/icons-vue'
import { getAlertStats } from '../api/alertmanager'

export default {
  name: 'AlertManagerTest',
  components: {
    DataBoard,
    List,
    Setting,
    Mute,
    Connection
  },
  setup() {
    const router = useRouter()
    const testing = ref(false)

    const goToPage = (path) => {
      router.push(path)
    }

    const testAPI = async () => {
      testing.value = true
      try {
        await getAlertStats()
        ElMessage.success('API连接测试成功！')
      } catch (error) {
        ElMessage.error('API连接测试失败: ' + (error.response?.data?.message || error.message))
      } finally {
        testing.value = false
      }
    }

    return {
      testing,
      goToPage,
      testAPI
    }
  }
}
</script>

<style scoped>
.alert-test-container {
  padding: var(--spacing-lg);
  background: var(--bg-page);
  min-height: calc(100vh - 60px);
}

.test-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: center;
  align-items: center;
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
}

.test-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.navigation-buttons h3,
.api-info h3,
.features-info h3 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: 600;
}

.feature-card {
  height: 100%;
}

.feature-card h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--primary-color);
  font-size: var(--font-size-base);
}

.feature-card ul {
  margin: 0;
  padding-left: var(--spacing-lg);
}

.feature-card li {
  margin-bottom: var(--spacing-xs);
  color: var(--text-regular);
}

.el-alert ul {
  margin: var(--spacing-sm) 0 0 0;
  padding-left: var(--spacing-lg);
}

.el-alert li {
  margin-bottom: var(--spacing-xs);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .alert-test-container {
    padding: var(--spacing-sm);
  }

  .navigation-buttons .el-col,
  .features-info .el-col {
    margin-bottom: var(--spacing-md);
  }
}
</style>
