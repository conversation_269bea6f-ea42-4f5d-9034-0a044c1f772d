# AlertManager 告警管理系统

## 📋 概述

AlertManager告警管理系统是一个基于Vue3的前端告警管理界面，用于管理和监控Prometheus/Alertmanager发送的告警数据。系统提供了完整的告警查询、管理、配置和统计功能。

## 🚀 功能特性

### 核心功能
- **告警查询** - 支持多条件筛选、分页显示、实时状态更新
- **告警管理** - 单个/批量解决告警、告警详情查看
- **配置管理** - Webhook配置、静默规则管理
- **统计分析** - 实时统计数据、图表可视化、趋势分析
- **权限控制** - 基于JWT的认证和权限管理

### 界面特性
- **响应式设计** - 支持桌面端和移动端
- **现代化UI** - 基于Element Plus的美观界面
- **实时更新** - 支持数据实时刷新
- **用户友好** - 直观的操作界面和交互体验

## 📁 文件结构

```
src/
├── api/
│   └── alertmanager.js          # AlertManager API服务
├── views/
│   ├── AlertManagerDashboard.vue    # 告警统计仪表板
│   ├── AlertManagerList.vue         # 告警列表页面
│   ├── AlertManagerDetail.vue       # 告警详情页面
│   ├── AlertManagerWebhookConfig.vue # Webhook配置管理
│   ├── AlertManagerSilenceRules.vue  # 静默规则管理
│   └── AlertManagerTest.vue         # 系统测试页面
└── router/
    └── index.js                 # 路由配置（已更新）
```

## 🔧 安装和配置

### 1. 依赖要求
- Vue 3.3+
- Element Plus 2.4+
- Vue Router 4.2+
- Axios 1.6+
- ECharts 5.4+ (用于图表显示)

### 2. 路由配置
系统已自动配置以下路由：

```javascript
// AlertManager 相关路由
'/alertmanager/dashboard'      // 告警仪表板
'/alertmanager/list'          // 告警列表
'/alertmanager/detail/:id'    // 告警详情
'/alertmanager/webhook-config' // Webhook配置
'/alertmanager/silence-rules'  // 静默规则
'/alertmanager/test'          // 系统测试
```

### 3. API配置
后端API基础URL: `http://iyunwei.serviceshare.com/api`

认证方式: JWT Bearer Token

## 📖 使用指南

### 告警列表页面 (`/alertmanager/list`)
- **搜索筛选**: 支持按日期范围、状态、等级、分类、负责人筛选
- **批量操作**: 支持批量选择和批量解决告警
- **卡片显示**: 以卡片形式展示告警信息，支持响应式布局
- **分页浏览**: 支持自定义每页显示数量

### 告警详情页面 (`/alertmanager/detail/:id`)
- **基本信息**: 显示告警的完整基本信息
- **原始数据**: 支持格式化和JSON两种显示方式
- **操作功能**: 支持标记解决、复制数据等操作
- **导航功能**: 支持返回列表页面

### Webhook配置管理 (`/alertmanager/webhook-config`)
- **配置列表**: 表格形式显示所有Webhook配置
- **增删改查**: 支持完整的CRUD操作
- **状态切换**: 支持快速启用/禁用配置
- **表单验证**: 完整的表单验证和错误提示

### 静默规则管理 (`/alertmanager/silence-rules`)
- **规则管理**: 支持创建、编辑、删除静默规则
- **模式匹配**: 支持正则表达式匹配告警标题
- **时间控制**: 支持设置静默时长（1-168小时）
- **状态管理**: 支持启用/禁用规则

### 告警统计仪表板 (`/alertmanager/dashboard`)
- **统计卡片**: 显示总告警数、活跃告警、已解决、今日告警
- **图表展示**: 饼图显示等级分布，柱状图显示分类分布
- **详细统计**: 表格形式显示详细统计信息
- **快捷导航**: 提供快速跳转到其他功能页面

## 🔌 API接口

### 告警查询接口
```javascript
// 获取告警列表
GET /alertmanager/logs?page=1&per_page=20&status=active

// 获取告警详情
GET /alertmanager/logs/{id}

// 获取告警统计
GET /alertmanager/logs/stats
```

### 告警管理接口
```javascript
// 标记单个告警解决
PUT /alertmanager/logs/{id}/resolve

// 批量标记告警解决
PUT /alertmanager/logs/batch-resolve
Body: { "alert_ids": [123, 124, 125] }
```

### 配置管理接口
```javascript
// Webhook配置
GET/POST/PUT/DELETE /alertmanager/config/webhooks

// 静默规则
GET/POST/PUT/DELETE /alertmanager/config/silence-rules
```

## 🎨 样式和主题

系统使用统一的CSS变量系统，支持：
- **颜色主题**: 主色调、辅助色、中性色
- **间距系统**: 统一的间距规范
- **字体系统**: 多级字体大小
- **响应式断点**: 支持多种屏幕尺寸
- **动画效果**: 平滑的过渡动画

## 📱 响应式设计

系统针对不同屏幕尺寸进行了优化：

- **桌面端** (>768px): 完整功能展示
- **平板端** (768px-480px): 适配中等屏幕
- **移动端** (<480px): 移动优化布局

## 🔒 权限控制

系统集成了完整的权限控制：
- **JWT认证**: 基于Token的用户认证
- **权限检查**: 不同操作需要不同权限
- **自动跳转**: 未认证用户自动跳转登录页

## 🧪 测试

访问 `/alertmanager/test` 页面可以：
- 查看系统功能概览
- 测试API连接
- 快速导航到各功能页面
- 查看接口文档

## 🚀 部署说明

1. 确保后端API服务正常运行
2. 配置正确的API基础URL
3. 确保JWT认证正常工作
4. 配置相应的菜单权限

## 📞 技术支持

如有问题，请检查：
1. 浏览器控制台错误信息
2. 网络请求状态
3. JWT Token有效性
4. 后端API服务状态

---

**注意**: 本系统需要配合后端AlertManager API服务使用，请确保后端服务正常运行并且网络连接正常。
