import api from './index'

// 分析wiki页面中的审批链接接口
export const analyzeWikiPage = (data) => {
  return api.post('/wiki/analyze', data, {
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 从指定wiki页面提取审批链接（不检查状态）接口
export const extractWikiLinks = (data) => {
  return api.post('/wiki/extract-links', data, {
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 检查指定审批链接的状态接口
export const checkLinkStatus = (data) => {
  return api.post('/wiki/check-status', data, {
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// SQL工单一键审批接口
export const batchApproveSql = (data) => {
  return api.post('/wiki/batch-approve-sql', data, {
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 获取待运维组审批的SQL工单列表
export const getPendingSqlWorkflows = (params) => {
  return api.get('/wiki/pending-sql-workflows', {
    params,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 根据工单ID一键执行审批
export const approveSqlWorkflow = (data) => {
  return api.post('/wiki/approve-sql-workflow', data, {
    headers: {
      'Content-Type': 'application/json'
    }
  })
}
